import {RouteRecordRaw} from 'vue-router'
// 使用函数导入Layout组件，避免循环依赖
const getLayout = () => import('@/components/layout/index.vue')

/**
 * 路由配置类
 * 用于管理系统的三部分路由：
 * 1. 公共路由（不需要登录）
 * 2. 基础路由（需要登录但不需要权限）
 * 3. 动态路由（需要登录且需要权限，从后端获取）
 */
export class RouteConfig {
    /**
     * 公共路由
     * 所有用户都可以访问的路由，不需要登录
     */
    static publicRoutes: RouteRecordRaw[] = [
        {
            path: '/search',
            component: () => import('@/views/project/search.vue'),
            name: 'searchIndex',
            meta: {
                inMenu: true,
                openInNewTab: true,
                title: '项目检索',
                icon: 'el-icon-search',
                order: 4,
                titleKey: 'menu.trialSearch'
            },
        },
        {
            path: '/project/view/:key/:isHistory',
            component: () => import('@/views/project/view.vue'),
            name: 'publicProjectView',
            meta: {title: '查看项目', icon: 'el-icon-view', hideInMenu: true}
        },
        {
            path: '/project/history/:businessId',
            component: () => import('@/views/project/history.vue'),
            name: 'publicHistory',
            meta: {title: '历史版本', icon: 'el-icon-view', hideInMenu: true}
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/login/index.vue'),
            meta: {title: '登录'}
        },

        {
            path: '/forgot-password',
            component: () => import('@/views/forgot-password/index.vue'),
            meta: {title: '忘记密码'}
        },
        {
            path: '/404',
            component: () => import('@/views/error/404.vue'),
            meta: {title: '404', hidden: true}
        },
        {
            path: '/403',
            component: () => import('@/views/error/403.vue'),
            meta: {title: '403', hidden: true}
        },
        {
            path: '/register',
            component: () => import('@/views/register/index.vue'),
            meta: {title: '注册', hidden: true}
        }
    ]

    /**
     * 基础路由
     * 登录后默认可访问的路由，不需要特殊权限
     */
    static baseRoutes: RouteRecordRaw[] = [
        {
            path: '/',
            component: getLayout,
            redirect: '/dashboard',
            name: 'Root',
            meta: {
                title: '首页', icon: 'el-icon-menu', order: 1,
                titleKey: 'menu.dashboard'
            },
            children: [
                {
                    path: 'dashboard',
                    component: () => import('@/views/dashboard/index.vue'),
                    name: 'dashboard',
                    meta: {title: '首页', icon: 'el-icon-menu', hideInMenu: true}
                },
            ]
        },








        {
            path: '/system',
            component: getLayout,
            name: 'system',
            meta: {title: '系统管理', icon: 'el-icon-setting', order: 10, titleKey: 'menu.system'},
            redirect: '/system/user',
            children: [
                {
                    path: '/watermark/test',
                    component: () => import('@/views/test/simple-watermark.vue'),
                    name: 'watermark',
                    meta: {
                        openInNewTab: true,
                        title: '暗水印测试',
                        icon: 'el-icon-menu',
                        order: 97,
                        hideInMenu: true,
                    }
                },
                {
                    path: '/watermark/analyzer',
                    component: () => import('@/views/tools/watermark-analyzer.vue'),
                    name: 'watermarkAnalyzer',
                    meta: {
                        openInNewTab: true,
                        title: '暗水印分析',
                        icon: 'el-icon-menu',
                        order: 97,
                        hideInMenu: true,
                    }
                },
                {
                    path: '/watermark/strength',
                    component: () => import('@/views/watermark-strength.vue'),
                    name: 'watermarkStrength',
                    meta: {
                        openInNewTab: true,
                        title: '暗水印增强分析',
                        icon: 'el-icon-menu',
                        order: 97,
                        hideInMenu: true,
                    }
                },
            ]
        },


        {
            path: '/review',
            component: () => import('@/views/review.vue'),
            name: 'reviewRule',
            meta: {
                openInNewTab: true,
                title: '初级临床试验审核工作规程',
                icon: 'el-icon-menu',
                order: 97,
                hideInMenu: true,
            }
        },

        {
            path: '/project',
            component: getLayout,
            name: 'project',
            meta: {title: '项目中心', icon: 'el-icon-menu', order: 2, hideInMenu: true, titleKey: 'menu.projectCenter'},
            children: [
                {
                    path: '/project/user/project-extract',
                    component: () => import('@/views/project/user/project-extract.vue'),
                    name: 'projectUserProjectExtract',
                    meta: {title: '提取新项目', hideInMenu: true}
                },
                {
                    path: '/project/user/project-add/:businessId',
                    component: () => import('@/views/project/user/project-add.vue'),
                    name: 'projectUserProjectAddEdit',
                    meta: {title: '修改项目', hideInMenu: true}
                },
                {
                    path: '/project/user/project-view/:businessId',
                    component: () => import('@/views/project/project-view.vue'),
                    name: 'projectUserProjectView',
                    meta: {title: '查看项目', hideInMenu: true}
                },
                {
                    path: '/project/user/project-view/:businessId/:version',
                    component: () => import('@/views/project/project-view.vue'),
                    name: 'projectUserProjectViewWithVersion',
                    meta: {title: '查看项目', hideInMenu: true}
                },
                {
                    path: '/project/user/project-edit-apply/:businessId',
                    component: () => import('@/views/project/user/project-edit-apply.vue'),
                    name: 'projectUserEditApplyProjectView',
                    meta: {title: '再申请修改', hideInMenu: true}
                }, {
                    path: '/project/user/project-edit-view/:businessId',
                    component: () => import('@/views/project/user/project-edit-view.vue'),
                    name: 'projectUserEditViewProjectView',
                    meta: {title: '再申请查看', hideInMenu: true}
                },
            ]
        },
        {
            path: '/profile',
            component: getLayout,
            redirect: '/profile/index',
            meta: {title: '个人中心', icon: 'el-icon-user', order: 99, titleKey: 'menu.profileIndex'},
            children: [
                {
                    path: '/profile/index',
                    component: () => import('@/views/profile/index.vue'),
                    name: 'Profile',
                    meta: {title: '个人资料', icon: 'el-icon-user', titleKey: 'menu.profileIndex'}
                },
                {
                    path: '/profile/change-password',
                    component: () => import('@/views/profile/change-password.vue'),
                    name: 'ChangePassword',
                    meta: {title: '修改密码', icon: 'el-icon-key', titleKey: 'menu.changePassword'}
                }
            ]
        },

    ]


    static permissionRoutes: RouteRecordRaw[] = [
        {
            path: '/project/user/add',
            component: getLayout,
            redirect: '/project/user/project-add',
            name: 'projectUserAdd',
            meta: {
                // openInNewTab: true,
                title: '注册新项目', icon: 'el-icon-circle-plus', order: 1,
                titleKey: 'menu.projectUserAdd'
            },
            children: [
                {
                    path: '/project/user/project-add',
                    component: () => import('@/views/project/user/project-add.vue'),
                    name: 'projectUserProjectAdd',
                    meta: {title: '注册新项目', hideInMenu: true}
                }
            ]
        },
        {
            path: '/project',
            component: getLayout,
            name: 'project',
            meta: {title: '项目中心', icon: 'el-icon-menu', order: 2, titleKey: 'menu.projectCenter'},
            children: [
                {
                    path: '/project/system/system-all-list',
                    component: () => import('@/views/project/system/system-all-list.vue'),
                    name: 'projectSystemAllList',
                    meta: {title: '所有项目', titleKey: 'menu.projectSystemAllList'}
                },
                {
                    path: '/project/system/system-project-edit/:businessId/:version',
                    component: () => import('@/views/project/system/system-project-edit.vue'),
                    name: 'projectSystemEdit',
                    meta: {title: '项目维护', titleKey: 'menu.projectSystemEdit', hideInMenu: true}
                },
                {
                    path: '/project/user/all-list',
                    component: () => import('@/views/project/user/all-list.vue'),
                    name: 'projectUserAllList',
                    meta: {title: '我的项目', titleKey: 'menu.projectUserAllList'}
                },
                {
                    path: '/project/user/pending-submit',
                    component: () => import('@/views/project/user/pending-submit.vue'),
                    name: 'projectUserPendingSubmit',
                    meta: {title: '待提交项目', titleKey: 'menu.projectUserPendingSubmit'}
                },
                {
                    path: '/project/user/pending-approval',
                    component: () => import('@/views/project/user/pending-approval.vue'),
                    name: 'projectUserPendingApproval',
                    meta: {title: '待审核项目', titleKey: 'menu.projectUserPendingApproval'}
                },
                {
                    path: '/project/user/approved-list',
                    component: () => import('@/views/project/user/approved-list.vue'),
                    name: 'projectUserApprovedList',
                    meta: {title: '已通过项目', titleKey: 'menu.projectUserApprovedList'}
                },
                {
                    path: '/project/user/project-extract',
                    component: () => import('@/views/project/user/project-extract.vue'),
                    name: 'projectUserProjectExtract',
                    meta: {title: '提取新项目', hideInMenu: true}
                },


            ]
        },
        {
            path: '/project-approval',
            component: getLayout,
            name: 'projectApproval',
            meta: {title: '项目审核', icon: 'el-icon-check', order: 3, titleKey: 'menu.projectApproval'},
            children: [

                //  一级
                {
                    path: '/project/system/pending-judge-list',
                    component: () => import('@/views/project/system/pending-judge-list.vue'),
                    name: 'projectSystemPendingJudgeList',
                    meta: {title: '待判断项目', order: 1, titleKey: 'menu.projectSystemPendingJudgeList'}
                },
                {
                    path: '/project/system/pending-send-number-list',
                    component: () => import('@/views/project/system/pending-send-number-list.vue'),
                    name: 'projectSystemPendingSendNumberList',
                    meta: {title: '待发号项目', order: 2, titleKey: 'menu.projectSystemPendingSendNumberList'}
                },
                {
                    path: '/project/system/apply-edit-list',
                    component: () => import('@/views/project/system/apply-edit-list.vue'),
                    name: 'projectSystemApplyEditList',
                    meta: {title: '再修改申请项目', order: 3, titleKey: 'menu.projectSystemApplyEditList'}
                },
                {
                    path: '/project/system/pending-review-list-1',
                    component: () => import('@/views/project/system/pending-review-list-1.vue'),
                    name: 'projectSystemPendingReviewList',
                    meta: {title: '再修改复核项目', order: 4, titleKey: 'menu.projectSystemPendingReviewList'}
                },
                {
                    path: '/project/system/return-edit-list',
                    component: () => import('@/views/project/system/return-edit-list.vue'),
                    name: 'projectSystemReturnEditList',
                    meta: {title: '再修改退回项目', order: 5, titleKey: 'menu.projectSystemReturnEditList'}
                }, {
                    path: '/project/system/approved-list',
                    component: () => import('@/views/project/system/approved-list.vue'),
                    name: 'projectSystemApprovedList',
                    meta: {title: '已发号项目', order: 6, titleKey: 'menu.projectSystemApprovedList'}
                },
                {
                    path: '/project/system/non-traditional-list',
                    component: () => import('@/views/project/system/non-traditional-list.vue'),
                    name: 'projectSystemNonTraditionalList',
                    meta: {title: '非传统医学项目', order: 7, titleKey: 'menu.projectSystemNonTraditionalList'}
                },
                {
                    path: '/project/system/all-submitted-list',
                    component: () => import('@/views/project/system/all-submitted-list.vue'),
                    name: 'projectSystemAllSubmittedList',
                    meta: {title: '审核状态查询', order: 8, titleKey: 'menu.projectSystemAllSubmittedList'}
                },
                {
                    path: '/project/system/project-judge/:businessId',
                    component: () => import('@/views/project/system/project-judge.vue'),
                    name: 'projectSystemProjectJudge',
                    meta: {title: '判断项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-review-edit/:businessId',
                    component: () => import('@/views/project/system/project-review-edit.vue'),
                    name: 'projectSystemProjectReviewEdit',
                    meta: {title: '再修改项目审核', hideInMenu: true}
                },
                {
                    path: '/project/system/project-send-number/:businessId',
                    component: () => import('@/views/project/system/project-send-number.vue'),
                    name: 'projectSystemProjectSendNumber',
                    meta: {title: '审核项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-edit-review/:businessId',
                    component: () => import('@/views/project/system/project-edit-review.vue'),
                    name: 'projectUserEditReviewProjectView',
                    meta: {title: '再申请审核', hideInMenu: true}
                },
                {
                    path: '/project/system/project-recall/:businessId',
                    component: () => import('@/views/project/system/project-recall.vue'),
                    name: 'projectRecall',
                    meta: {title: '项目召回', hideInMenu: true}
                },


//二级
                {
                    path: '/project/system/pending-assign-list',
                    component: () => import('@/views/project/system/pending-assign-list.vue'),
                    name: 'projectSystemPendingAssignList',
                    meta: {title: '待分配项目', order: 9, titleKey: 'menu.projectSystemPendingAssignList'}
                },
                {
                    path: '/project/system/pending-review-list-2',
                    component: () => import('@/views/project/system/pending-review-list-2.vue'),
                    name: 'projectSystemPendingReviewList2',
                    meta: {title: '待核审项目', order: 10, titleKey: 'menu.projectSystemPendingReviewList2'}
                },
                {
                    path: '/project/system/review-returned-list-2',
                    component: () => import('@/views/project/system/review-returned-list-2.vue'),
                    name: 'projectSystemReviewReturnedList2',
                    meta: {title: '已退回项目', order: 11, titleKey: 'menu.projectSystemReviewReturnedList2'}
                },
                {
                    path: '/project/system/pending-approved-list-2',
                    component: () => import('@/views/project/system/pending-approved-list-2.vue'),
                    name: 'projectSystemPendingApprovedList2',
                    meta: {title: '已核审通过项目', order: 12, titleKey: 'menu.projectSystemPendingApprovedList2'}
                },
                {
                    path: '/project/system/approved-list-2',
                    component: () => import('@/views/project/system/approved-list-2.vue'),
                    name: 'projectSystemApprovedList2',
                    meta: {title: '已发号项目', order: 13, titleKey: 'menu.projectSystemApprovedList2'}
                },

                {
                    path: '/project/system/re-assign-list',
                    component: () => import('@/views/project/system/re-assign-list.vue'),
                    name: 'projectSystemReAssignList',
                    meta: {title: '重新分配项目', order: 14, titleKey: 'menu.projectSystemReAssignList'}
                },
                {
                    path: '/project/system/project-review-2/:businessId',
                    component: () => import('@/views/project/system/project-review-2.vue'),
                    name: 'projectSystemProjectReview2',
                    meta: {title: '审核项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-assign/:businessId',
                    component: () => import('@/views/project/system/project-assign.vue'),
                    name: 'projectSystemProjectAssign',
                    meta: {title: '分配项目', hideInMenu: true}
                },

//三级
                {
                    path: '/project/system/pending-assign-review-list',
                    component: () => import('@/views/project/system/pending-assign-review-list.vue'),
                    name: 'projectSystemPendingAssignReviewList',
                    meta: {title: '待分配项目', order: 15, titleKey: 'menu.projectSystemPendingAssignReviewList'}
                },
                {
                    path: '/project/system/pending-review-list-3',
                    component: () => import('@/views/project/system/pending-review-list-3.vue'),
                    name: 'projectSystemPendingReviewList3',
                    meta: {title: '待复审项目', order: 16, titleKey: 'menu.projectSystemPendingReviewList3'}
                },
                {
                    path: '/project/system/pending-approved-list-3',
                    component: () => import('@/views/project/system/pending-approved-list-3.vue'),
                    name: 'projectSystemPendingApprovedList3',
                    meta: {title: '已复审通过项目', order: 17, titleKey: 'menu.projectSystemPendingApprovedList3'}
                },
                {
                    path: '/project/system/review-returned-list-3',
                    component: () => import('@/views/project/system/review-returned-list-3.vue'),
                    name: 'projectSystemReviewReturnedList3',
                    meta: {title: '已退回项目', order: 18, titleKey: 'menu.projectSystemReviewReturnedList3'}
                },
                {
                    path: '/project/system/approved-list-3',
                    component: () => import('@/views/project/system/approved-list-3.vue'),
                    name: 'projectSystemApprovedList3',
                    meta: {title: '已发号项目', order: 19, titleKey: 'menu.projectSystemApprovedList3'}
                },
                {
                    path: '/project/system/project-assign-review/:businessId',
                    component: () => import('@/views/project/system/project-assign-review.vue'),
                    name: 'projectSystemProjectAssignReview',
                    meta: {title: '分审项目', hideInMenu: true}
                },
                {
                    path: '/project/system/project-review-3/:businessId',
                    component: () => import('@/views/project/system/project-review-3.vue'),
                    name: 'projectSystemProjectReview3',
                    meta: {title: '审核项目', hideInMenu: true}
                },


//四级
                {
                    path: '/project/system/pending-review-list-4',
                    component: () => import('@/views/project/system/pending-review-list-4.vue'),
                    name: 'projectSystemPendingReviewList4',
                    meta: {title: '待初审项目', order: 20, titleKey: 'menu.projectSystemPendingReviewList4'}
                },
                {
                    path: '/project/system/pending-approved-list-4',
                    component: () => import('@/views/project/system/pending-approved-list-4.vue'),
                    name: 'projectSystemPendingApprovedList4',
                    meta: {title: '已初审通过项目', order: 21, titleKey: 'menu.projectSystemPendingApprovedList4'}
                },
                {
                    path: '/project/system/review-returned-list-4',
                    component: () => import('@/views/project/system/review-returned-list-4.vue'),
                    name: 'projectSystemReviewReturnedList4',
                    meta: {title: '已退回项目', order: 22, titleKey: 'menu.projectSystemReviewReturnedList4'}
                },
                {
                    path: '/project/system/approved-list-4',
                    component: () => import('@/views/project/system/approved-list-4.vue'),
                    name: 'projectSystemApprovedList4',
                    meta: {title: '已发号项目', order: 23, titleKey: 'menu.projectSystemApprovedList4'}
                },
                {
                    path: '/project/system/project-review-4/:businessId',
                    component: () => import('@/views/project/system/project-review-4.vue'),
                    name: 'projectSystemProjectReview4',
                    meta: {title: '审核项目', hideInMenu: true}
                },


            ]
        },
        {
            path: '/system',
            component: getLayout,
            name: 'system',
            meta: {title: '系统管理', icon: 'el-icon-setting', order: 10, titleKey: 'menu.system'},
            redirect: '/system/user',
            children: [
                {
                    path: '/system/logging',
                    name: 'system:logging',
                    component: () => import('@/views/logging/index.vue'),
                    meta: {title: '日志管理', icon: 'el-icon-box', order: 0, titleKey: 'menu.systemLogging'}
                },
                {
                    path: '/system/user',
                    name: 'system:user',
                    component: () => import('@/views/system/user/index.vue'),
                    meta: {title: '用户管理', icon: 'el-icon-user', order: 1, titleKey: 'menu.systemUser'}
                },
                {
                    path: '/system/role',
                    name: 'system:role',
                    component: () => import('@/views/system/role/index.vue'),
                    meta: {title: '角色管理', icon: 'el-icon-key', order: 2, titleKey: 'menu.systemRole'}
                },
                {
                    path: '/system/organization',
                    name: 'system:organization',
                    component: () => import('@/views/system/organization/index.vue'),
                    meta: {
                        title: '组织管理',
                        icon: 'el-icon-office-building',
                        order: 3,
                        titleKey: 'menu.systemOrganization'
                    }
                },
                {
                    path: '/system/position',
                    name: 'system:position',
                    component: () => import('@/views/system/position/index.vue'),
                    meta: {title: '岗位管理', icon: 'el-icon-user-filled', order: 4, titleKey: 'menu.systemPosition'}
                },
                {
                    path: '/system/permission',
                    name: 'system:permission',
                    component: () => import('@/views/system/permission/index.vue'),
                    meta: {title: '权限管理', icon: 'el-icon-key', order: 5, titleKey: 'menu.systemPermission'}
                }
            ]
        },
        {
            path: '/files',
            name: 'files',
            component: getLayout,
            meta: {title: '存储管理', icon: 'el-icon-folder', order: 11, titleKey: 'menu.files'},
            redirect: '/files/storage',
            children: [
                {
                    path: '/files/storage',
                    name: 'files:storage',
                    component: () => import('@/views/files/storage/index.vue'),
                    meta: {title: '存储管理', icon: 'el-icon-folder', order: 1, titleKey: 'menu.filesStorage'}
                },
                {
                    path: '/files/file-type',
                    name: 'files:file-type',
                    component: () => import('@/views/files/file-type/index.vue'),
                    meta: {title: '文件类型管理', icon: 'el-icon-document', order: 2, titleKey: 'menu.filesFileType'}
                }
            ]
        },
        {
            path: '/form-management',
            name: 'form-management',
            component: getLayout,
            meta: {title: '表单管理', icon: 'el-icon-document', order: 12, titleKey: 'menu.formManagement'},
            children: [
                {
                    path: '/form-management/form-list',
                    name: 'form-management:form-list',
                    component: () => import('@/views/form-management/form-list/index.vue'),
                    meta: {title: '表单定义', icon: 'el-icon-document-copy', order: 1, titleKey: 'menu.formList'}
                },
                {
                    path: '/form-management/form-designer/:id',
                    name: 'form-management:design',
                    component: () => import('@/views/form-management/form-list/form-designer.vue'),
                    meta: {title: '表单设计', icon: 'el-icon-document-copy', order: 2, hideInMenu: true}
                }
            ]
        },
        {
            path: '/messaging',
            name: 'Messaging',
            component: getLayout,
            meta: {
                title: '消息管理',
                icon: 'Message',
                titleKey: 'menu.messaging',
                order: 13
            },
            children: [
                {
                    path: '/messaging/provider',
                    name: 'MessageProvider',
                    component: () => import('@/views/messaging/provider/index.vue'),
                    meta: {
                        title: '服务商管理',
                        icon: 'Setting',
                        titleKey: 'menu.messagingProvider'
                    }
                },
                {
                    path: '/messaging/account',
                    name: 'MessageAccount',
                    component: () => import('@/views/messaging/account/index.vue'),
                    meta: {
                        title: '账户管理',
                        icon: 'User',
                        titleKey: 'menu.messagingAccount'
                    }
                },
                {
                    path: '/messaging/template',
                    name: 'MessageTemplate',
                    component: () => import('@/views/messaging/template/index.vue'),
                    meta: {
                        title: '模板管理',
                        icon: 'Document',
                        titleKey: 'menu.messagingTemplate'
                    }
                },
                {
                    path: '/messaging/send-records',
                    name: 'MessageSendRecords',
                    component: () => import('@/views/messaging/send-records/index.vue'),
                    meta: {
                        title: '发送记录',
                        icon: 'List',
                        titleKey: 'menu.messagingSendRecords'
                    }
                },
                // {
                //     path: '/messaging/send-test',
                //     name: 'MessageSendTest',
                //     component: () => import('@/views/messaging/send-test/index.vue'),
                //     meta: {
                //         title: '发送测试',
                //         icon: 'Promotion',
                //         titleKey: 'menu.messagingSendTest'
                //     }
                // }
            ]
        }
        // 其它如 API 权限根节点等可根据需要补充
    ]

    /**
     * 所有不需要动态判断权限的路由
     * 包括公共路由和基础路由
     */
    static constantRoutes: RouteRecordRaw[] = [
        ...RouteConfig.publicRoutes,
        ...RouteConfig.baseRoutes
    ]

    /**
     * 所有路由的末尾路由
     * 必须放在最后，用于捕获所有未匹配的路由
     */
    static fallbackRoute: RouteRecordRaw = {
        path: '/:pathMatch(.*)*',
        redirect: '/404',
        meta: {hidden: true}
    }

    /**
     * 白名单路由
     * 未登录也可以访问的路由
     */
    // static whiteList: string[] = ['/login', '/internal/login', '/register', '/forgot-password', '/404']
}
