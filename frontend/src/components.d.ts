/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppMain: typeof import('./components/layout/AppMain.vue')['default']
    BaseField: typeof import('./components/dynamic-form/form-fields/BaseField.vue')['default']
    BaseSubFormField: typeof import('./components/dynamic-form/form-fields/BaseSubFormField.vue')['default']
    Breadcrumb: typeof import('./components/layout/Breadcrumb.vue')['default']
    CheckboxField: typeof import('./components/dynamic-form/form-fields/CheckboxField/index.vue')['default']
    DataTable: typeof import('./components/common/DataTable.vue')['default']
    DateField: typeof import('./components/dynamic-form/form-fields/DateField/index.vue')['default']
    DatePropertyEditor: typeof import('./components/dynamic-form/field-property-editors/DatePropertyEditor.vue')['default']
    DateRangeField: typeof import('./components/dynamic-form/form-fields/DateRangeField/index.vue')['default']
    DateRangePropertyEditor: typeof import('./components/dynamic-form/field-property-editors/DateRangePropertyEditor.vue')['default']
    DateTimePicker: typeof import('./components/common/DateTimePicker.vue')['default']
    DefaultPropertyEditor: typeof import('./components/dynamic-form/field-property-editors/DefaultPropertyEditor.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTour: typeof import('element-plus/es')['ElTour']
    ElTourStep: typeof import('element-plus/es')['ElTourStep']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    ExpressionEditor: typeof import('./components/dynamic-form/ExpressionEditor.vue')['default']
    ExtractionResultViewer: typeof import('./components/watermark/ExtractionResultViewer.vue')['default']
    FieldAnnotationDialog: typeof import('./components/dynamic-form/FieldAnnotationDialog.vue')['default']
    FieldEditDialog: typeof import('./components/dynamic-form/FieldEditDialog.vue')['default']
    FileDownloader: typeof import('./components/common/FileDownloader.vue')['default']
    FileField: typeof import('./components/dynamic-form/form-fields/FileField/index.vue')['default']
    FileList: typeof import('./components/common/FileList.vue')['default']
    FilePropertyEditor: typeof import('./components/dynamic-form/field-property-editors/FilePropertyEditor.vue')['default']
    FileUploader: typeof import('./components/common/FileUploader.vue')['default']
    Footer: typeof import('./components/layout/Footer.vue')['default']
    HorizontalMenu: typeof import('./components/layout/HorizontalMenu.vue')['default']
    ImagePreview: typeof import('./components/common/ImagePreview.vue')['default']
    InputField: typeof import('./components/dynamic-form/form-fields/InputField/index.vue')['default']
    InputPropertyEditor: typeof import('./components/dynamic-form/field-property-editors/InputPropertyEditor.vue')['default']
    IntRangeField: typeof import('./components/dynamic-form/form-fields/IntRangeField/index.vue')['default']
    IntRangePropertyEditor: typeof import('./components/dynamic-form/field-property-editors/IntRangePropertyEditor.vue')['default']
    LangSelect: typeof import('./components/common/LangSelect.vue')['default']
    Layout: typeof import('./components/layout/index.vue')['default']
    Link: typeof import('./components/layout/Link.vue')['default']
    LogicExprEditor: typeof import('./components/dynamic-form/LogicExprEditor.vue')['default']
    Menu: typeof import('./components/layout/Menu.vue')['default']
    MultilangInputPropertyEditor: typeof import('./components/dynamic-form/field-property-editors/MultilangInputPropertyEditor.vue')['default']
    MultiOptionsPropertyEditor: typeof import('./components/dynamic-form/field-property-editors/MultiOptionsPropertyEditor.vue')['default']
    MultiSubFormField: typeof import('./components/dynamic-form/form-fields/MultiSubFormField/index.vue')['default']
    Navbar: typeof import('./components/layout/Navbar.vue')['default']
    NumberField: typeof import('./components/dynamic-form/form-fields/NumberField/index.vue')['default']
    NumberPropertyEditor: typeof import('./components/dynamic-form/field-property-editors/NumberPropertyEditor.vue')['default']
    OptionsPropertyEditor: typeof import('./components/dynamic-form/field-property-editors/OptionsPropertyEditor.vue')['default']
    PdfViewer: typeof import('./components/common/PdfViewer.vue')['default']
    RadioField: typeof import('./components/dynamic-form/form-fields/RadioField/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./components/common/SearchForm.vue')['default']
    SelectField: typeof import('./components/dynamic-form/form-fields/SelectField/index.vue')['default']
    Sidebar: typeof import('./components/layout/Sidebar.vue')['default']
    SidebarItem: typeof import('./components/layout/SidebarItem.vue')['default']
    SubFormField: typeof import('./components/dynamic-form/form-fields/SubFormField/index.vue')['default']
    TextareaField: typeof import('./components/dynamic-form/form-fields/TextareaField/index.vue')['default']
    TextareaMultilangField: typeof import('./components/dynamic-form/form-fields/TextareaMultilangField/index.vue')['default']
    TextMultilangField: typeof import('./components/dynamic-form/form-fields/TextMultilangField/index.vue')['default']
    UnitSelectField: typeof import('./components/dynamic-form/form-fields/UnitSelectField/index.vue')['default']
    ValueEditor: typeof import('./components/dynamic-form/ValueEditor.vue')['default']
    WatermarkExtractor: typeof import('./components/watermark/WatermarkExtractor.vue')['default']
    WatermarkManager: typeof import('./components/watermark/WatermarkManager.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
