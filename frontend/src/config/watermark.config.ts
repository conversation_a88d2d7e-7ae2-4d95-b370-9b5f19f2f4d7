/**
 * 水印系统全局配置
 */

export interface WatermarkEnvironmentConfig {
  enabled: boolean
  techniques: ('frequency' | 'css' | 'svg' | 'webgl')[]
  strength: number
  refreshInterval: number
  debugMode: boolean
  healthCheck: boolean
  autoRestart: boolean
}

export interface WatermarkGlobalConfig {
  development: WatermarkEnvironmentConfig
  test: WatermarkEnvironmentConfig
  staging: WatermarkEnvironmentConfig
  production: WatermarkEnvironmentConfig
}

// 全局水印配置
export const WATERMARK_CONFIG: WatermarkGlobalConfig = {
  // 开发环境 - 便于调试和观察
  development: {
    enabled: true,
    techniques: ['css'],
    strength: 0.02, // 较高强度，便于观察
    refreshInterval: 30000, // 30秒
    debugMode: true,
    healthCheck: true,
    autoRestart: true
  },

  // 测试环境 - 平衡可见性和隐蔽性
  test: {
    enabled: true,
    techniques: ['css'],
    strength: 0.005, // 中等强度
    refreshInterval: 45000, // 45秒
    debugMode: true,
    healthCheck: true,
    autoRestart: true
  },

  // 预发布环境 - 接近生产环境
  staging: {
    enabled: true,
    techniques: ['frequency', 'css'],
    strength: 0.002, // 较低强度
    refreshInterval: 60000, // 1分钟
    debugMode: false,
    healthCheck: true,
    autoRestart: true
  },

  // 生产环境 - 最高隐蔽性
  production: {
    enabled: true,
    techniques: ['frequency', 'css'],
    strength: 0.001, // 极低强度
    refreshInterval: 60000, // 1分钟
    debugMode: false,
    healthCheck: true,
    autoRestart: false // 生产环境谨慎重启
  }
}

// 获取当前环境配置
export function getCurrentWatermarkConfig(): WatermarkEnvironmentConfig {
  const env = import.meta.env.MODE as keyof WatermarkGlobalConfig
  return WATERMARK_CONFIG[env] || WATERMARK_CONFIG.development
}

// 水印数据字段配置
export const WATERMARK_FIELDS = {
  // 必需字段
  required: ['user', 'timestamp', 'path'] as const,
  
  // 可选字段
  optional: ['params', 'sessionId', 'userAgent', 'deviceInfo'] as const,
  
  // 敏感字段（需要特殊处理）
  sensitive: ['userAgent', 'deviceInfo'] as const
}

// 水印安全配置
export const WATERMARK_SECURITY = {
  // 数据加密
  encryption: {
    enabled: true,
    algorithm: 'AES-256-GCM',
    keyRotationInterval: 24 * 60 * 60 * 1000 // 24小时
  },
  
  // 数据混淆
  obfuscation: {
    enabled: true,
    userFieldObfuscation: true,
    timestampOffset: true
  },
  
  // 完整性校验
  integrity: {
    enabled: true,
    checksumAlgorithm: 'SHA-256'
  }
}

// 水印性能配置
export const WATERMARK_PERFORMANCE = {
  // 渲染优化
  rendering: {
    useRequestAnimationFrame: true,
    batchUpdates: true,
    maxConcurrentOperations: 3
  },
  
  // 内存管理
  memory: {
    maxCacheSize: 10 * 1024 * 1024, // 10MB
    gcInterval: 5 * 60 * 1000, // 5分钟
    autoCleanup: true
  },
  
  // 网络优化
  network: {
    enableCompression: true,
    maxRetries: 3,
    timeout: 5000
  }
}

// 水印兼容性配置
export const WATERMARK_COMPATIBILITY = {
  // 浏览器支持
  browsers: {
    chrome: { minVersion: 80, supported: true },
    firefox: { minVersion: 75, supported: true },
    safari: { minVersion: 13, supported: true },
    edge: { minVersion: 80, supported: true },
    ie: { minVersion: 11, supported: false }
  },
  
  // 设备支持
  devices: {
    desktop: { supported: true, optimized: true },
    tablet: { supported: true, optimized: true },
    mobile: { supported: true, optimized: false }
  },
  
  // 功能降级
  fallback: {
    enableGracefulDegradation: true,
    fallbackTechniques: ['css'],
    disableOnUnsupported: false
  }
}

// 导出便捷函数
export function isWatermarkEnabled(): boolean {
  return getCurrentWatermarkConfig().enabled
}

export function getWatermarkStrength(): number {
  return getCurrentWatermarkConfig().strength
}

export function getWatermarkTechniques(): string[] {
  return getCurrentWatermarkConfig().techniques
}

export function isDebugMode(): boolean {
  return getCurrentWatermarkConfig().debugMode
}

// 环境检测
export function getEnvironment(): keyof WatermarkGlobalConfig {
  return (import.meta.env.MODE as keyof WatermarkGlobalConfig) || 'development'
}

// 配置验证
export function validateWatermarkConfig(config: Partial<WatermarkEnvironmentConfig>): boolean {
  try {
    // 检查必需字段
    if (typeof config.enabled !== 'boolean') return false
    if (!Array.isArray(config.techniques)) return false
    if (typeof config.strength !== 'number' || config.strength < 0 || config.strength > 1) return false
    if (typeof config.refreshInterval !== 'number' || config.refreshInterval < 1000) return false
    
    return true
  } catch {
    return false
  }
}

// 动态配置更新（仅开发环境）
export function updateWatermarkConfig(
  env: keyof WatermarkGlobalConfig, 
  config: Partial<WatermarkEnvironmentConfig>
): boolean {
  if (import.meta.env.MODE !== 'development') {
    console.warn('配置更新仅在开发环境可用')
    return false
  }
  
  if (!validateWatermarkConfig(config)) {
    console.error('无效的水印配置')
    return false
  }
  
  Object.assign(WATERMARK_CONFIG[env], config)
  console.log(`水印配置已更新 [${env}]`, config)
  return true
}
