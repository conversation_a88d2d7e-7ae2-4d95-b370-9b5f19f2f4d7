/**
 * 水印服务管理器
 * 统一管理应用中的水印功能
 */

import { WatermarkComposer } from '@/utils/watermark-composer'
import { getCurrentWatermarkConfig, isWatermarkEnabled, isDebugMode } from '@/config/watermark.config'

export interface WatermarkData {
  user: string
  timestamp: number
  path: string
  params: any
  sessionId?: string
  userAgent?: string
  deviceInfo?: {
    screen: string
    platform: string
    language: string
  }
}

export interface WatermarkServiceConfig {
  enabled: boolean
  autoStart: boolean
  healthCheck: boolean
  debugMode: boolean
}

export type WatermarkStatus = 'disabled' | 'initializing' | 'active' | 'error' | 'paused'

export class WatermarkService {
  private composer: WatermarkComposer | null = null
  private container: HTMLElement | null = null
  private status: WatermarkStatus = 'disabled'
  private config: WatermarkServiceConfig
  private healthCheckTimer: number | null = null
  private sessionId: string = ''

  // 事件回调
  private onStatusChange?: (status: WatermarkStatus) => void
  private onError?: (error: Error) => void

  constructor(config: Partial<WatermarkServiceConfig> = {}) {
    const watermarkConfig = getCurrentWatermarkConfig()

    this.config = {
      enabled: isWatermarkEnabled(),
      autoStart: true,
      healthCheck: watermarkConfig.healthCheck,
      debugMode: isDebugMode(),
      ...config
    }

    this.sessionId = this.generateSessionId()
    this.log('水印服务已创建', { sessionId: this.sessionId, env: import.meta.env.MODE })
  }

  /**
   * 初始化水印服务
   */
  async initialize(container: HTMLElement, data: Partial<WatermarkData>): Promise<boolean> {
    this.log('开始初始化水印服务', { enabled: this.config.enabled, sessionId: this.sessionId })

    if (!this.config.enabled) {
      this.log('水印服务已禁用')
      return false
    }

    try {
      this.setStatus('initializing')
      this.container = container

      // 清理旧实例
      if (this.composer) {
        this.log('清理旧的水印实例')
        await this.destroy()
      }

      // 获取水印配置
      const watermarkConfig = this.getWatermarkConfig()
      this.log('获取水印配置', watermarkConfig)

      // 创建水印组合器
      this.composer = new WatermarkComposer(watermarkConfig)
      this.log('创建水印组合器成功')

      // 准备完整的水印数据
      const fullData = this.prepareWatermarkData(data)
      this.log('准备水印数据', { user: fullData.user, path: fullData.path, sessionId: fullData.sessionId })

      // 初始化水印
      await this.composer.initialize(container, fullData)
      this.log('水印组合器初始化成功')

      this.setStatus('active')
      this.log('水印服务初始化完成', {
        user: fullData.user,
        path: fullData.path,
        sessionId: this.sessionId,
        strength: watermarkConfig.globalAlpha
      })

      // 启动健康检查
      if (this.config.healthCheck) {
        this.startHealthCheck()
        this.log('健康检查已启动')
      }

      return true
    } catch (error) {
      this.setStatus('error')
      this.handleError(new Error(`水印初始化失败: ${error}`))
      return false
    }
  }

  /**
   * 更新水印数据
   */
  async updateData(data: Partial<WatermarkData>): Promise<boolean> {
    if (!this.composer || this.status !== 'active') {
      this.log('水印服务未激活，跳过更新')
      return false
    }

    try {
      const fullData = this.prepareWatermarkData(data)
      await this.composer.updateData(fullData)
      this.log('水印数据更新成功', { path: fullData.path, user: fullData.user })
      return true
    } catch (error) {
      this.handleError(new Error(`水印更新失败: ${error}`))
      return false
    }
  }

  /**
   * 暂停水印服务
   */
  pause(): void {
    if (this.status === 'active') {
      this.setStatus('paused')
      this.stopHealthCheck()
      this.log('水印服务已暂停')
    }
  }

  /**
   * 恢复水印服务
   */
  resume(): void {
    if (this.status === 'paused' && this.composer) {
      this.setStatus('active')
      if (this.config.healthCheck) {
        this.startHealthCheck()
      }
      this.log('水印服务已恢复')
    }
  }

  /**
   * 销毁水印服务
   */
  async destroy(): Promise<void> {
    this.log('开始销毁水印服务')

    try {
      // 停止健康检查
      this.stopHealthCheck()
      this.log('健康检查已停止')

      // 销毁水印组合器
      if (this.composer) {
        this.log('销毁水印组合器')
        this.composer.destroy()
        this.composer = null
      }

      // 清理容器引用
      this.container = null

      // 更新状态
      this.setStatus('disabled')
      this.log('水印服务已销毁', { sessionId: this.sessionId })
    } catch (error) {
      this.handleError(new Error(`水印销毁失败: ${error}`))
    }
  }

  /**
   * 重启水印服务
   */
  async restart(data?: Partial<WatermarkData>): Promise<boolean> {
    this.log('开始重启水印服务', { currentSessionId: this.sessionId })

    if (!this.container) {
      this.log('无容器信息，无法重启')
      return false
    }

    try {
      // 保存当前容器引用和数据
      const container = this.container
      const restartData = data || {}

      this.log('保存容器引用和重启数据', {
        containerExists: !!container,
        userData: restartData.user,
        userPath: restartData.path
      })

      // 销毁当前实例
      await this.destroy()
      this.log('当前实例已销毁')

      // 等待清理完成
      await new Promise(resolve => setTimeout(resolve, 200))

      // 生成新的会话ID
      const oldSessionId = this.sessionId
      this.sessionId = this.generateSessionId()
      this.log('生成新会话ID', {
        oldSessionId,
        newSessionId: this.sessionId
      })

      // 验证数据完整性
      if (!restartData.user || !restartData.path) {
        this.log('警告：重启数据不完整', restartData)
      }

      // 重新初始化
      const success = await this.initialize(container, restartData)

      if (success) {
        this.log('水印服务重启成功', {
          sessionId: this.sessionId,
          status: this.status
        })
      } else {
        this.log('水印服务重启失败', {
          sessionId: this.sessionId,
          status: this.status
        })
      }

      return success
    } catch (error) {
      this.handleError(new Error(`重启失败: ${error}`))
      return false
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): WatermarkStatus {
    return this.status
  }

  /**
   * 获取服务信息
   */
  getServiceInfo(): {
    status: WatermarkStatus
    sessionId: string
    config: any
    container: HTMLElement | null
  } {
    return {
      status: this.status,
      sessionId: this.sessionId,
      config: this.config,
      container: this.container
    }
  }

  /**
   * 获取会话ID
   */
  getSessionId(): string {
    return this.sessionId
  }

  /**
   * 设置事件监听器
   */
  setEventListeners(callbacks: {
    onStatusChange?: (status: WatermarkStatus) => void
    onError?: (error: Error) => void
  }): void {
    this.onStatusChange = callbacks.onStatusChange
    this.onError = callbacks.onError
  }

  /**
   * 获取水印配置
   */
  private getWatermarkConfig() {
    const config = getCurrentWatermarkConfig()

    return {
      techniques: config.techniques,
      globalAlpha: config.strength,
      dynamicRefresh: true,
      refreshInterval: config.refreshInterval
    }
  }

  /**
   * 准备完整的水印数据
   */
  private prepareWatermarkData(data: Partial<WatermarkData>): WatermarkData {
    return {
      user: data.user || 'unknown',
      timestamp: data.timestamp || Date.now(),
      path: data.path || window.location.pathname,
      params: data.params || {},
      sessionId: this.sessionId,
      userAgent: navigator.userAgent.substring(0, 50),
      deviceInfo: {
        screen: `${screen.width}x${screen.height}`,
        platform: navigator.platform,
        language: navigator.language
      },
      ...data
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
  }

  /**
   * 设置状态
   */
  private setStatus(status: WatermarkStatus): void {
    if (this.status !== status) {
      this.status = status
      this.onStatusChange?.(status)
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: Error): void {
    this.log('水印服务错误', error.message)
    this.onError?.(error)
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.stopHealthCheck()
    this.healthCheckTimer = window.setInterval(() => {
      this.performHealthCheck()
    }, 30000) // 30秒检查一次
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer)
      this.healthCheckTimer = null
    }
  }

  /**
   * 执行健康检查
   */
  private performHealthCheck(): void {
    if (this.status === 'active' && !this.composer) {
      this.log('健康检查失败：状态为active但composer为null')
      this.setStatus('error')
    }
  }

  /**
   * 日志输出
   */
  private log(message: string, data?: any): void {
    if (this.config.debugMode) {
      console.log(`[WatermarkService] ${message}`, data || '')
    }
  }
}

// 创建全局水印服务实例
export const watermarkService = new WatermarkService({
  enabled: isWatermarkEnabled(),
  autoStart: true,
  healthCheck: getCurrentWatermarkConfig().healthCheck,
  debugMode: isDebugMode()
})

// 导出便捷方法
export const initWatermark = (container: HTMLElement, data: Partial<WatermarkData>) => 
  watermarkService.initialize(container, data)

export const updateWatermark = (data: Partial<WatermarkData>) => 
  watermarkService.updateData(data)

export const destroyWatermark = () => 
  watermarkService.destroy()

export const getWatermarkStatus = () => 
  watermarkService.getStatus()
