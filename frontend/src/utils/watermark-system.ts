/**
 * 新一代水印系统
 * 具备抗干扰、抗压缩能力，支持后端提取
 */

export interface WatermarkData {
  user: string
  timestamp: number
  path: string
  sessionId: string
}

export interface WatermarkConfig {
  strength: number
  techniques: ('canvas' | 'css' | 'dom')[]
  antiCompression: boolean
  antiInterference: boolean
  debugMode: boolean
}

export class WatermarkSystem {
  private container: HTMLElement | null = null
  private config: WatermarkConfig
  private watermarkElements: HTMLElement[] = []
  private canvasElements: HTMLCanvasElement[] = []
  private isActive = false

  constructor(config: Partial<WatermarkConfig> = {}) {
    this.config = {
      strength: 0.003, // 极低强度，正常情况下不可见
      techniques: ['canvas', 'css', 'dom'],
      antiCompression: true,
      antiInterference: true,
      debugMode: import.meta.env.MODE === 'development',
      ...config
    }
  }

  /**
   * 初始化水印系统
   */
  async initialize(container: HTMLElement, data: WatermarkData): Promise<boolean> {
    try {
      this.container = container
      this.destroy() // 清理旧的水印

      // 多层次水印嵌入
      if (this.config.techniques.includes('canvas')) {
        await this.createCanvasWatermark(data)
      }
      
      if (this.config.techniques.includes('css')) {
        await this.createCSSWatermark(data)
      }
      
      if (this.config.techniques.includes('dom')) {
        await this.createDOMWatermark(data)
      }

      this.isActive = true
      this.log('水印系统初始化成功', data)
      return true
    } catch (error) {
      this.log('水印系统初始化失败', error)
      return false
    }
  }

  /**
   * 创建Canvas水印（抗压缩核心）
   */
  private async createCanvasWatermark(data: WatermarkData): Promise<void> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    // 设置高分辨率画布
    const scale = window.devicePixelRatio || 1
    canvas.width = window.innerWidth * scale
    canvas.height = window.innerHeight * scale
    canvas.style.width = '100vw'
    canvas.style.height = '100vh'
    canvas.style.position = 'fixed'
    canvas.style.top = '0'
    canvas.style.left = '0'
    canvas.style.pointerEvents = 'none'
    canvas.style.zIndex = '9999'
    canvas.style.mixBlendMode = 'multiply'
    canvas.style.opacity = this.config.strength.toString()
    
    ctx.scale(scale, scale)

    // 生成水印内容
    const watermarkText = this.encodeWatermarkData(data)
    
    // 多层次绘制策略
    await this.drawMultiLayerWatermark(ctx, watermarkText, canvas.width / scale, canvas.height / scale)
    
    this.container!.appendChild(canvas)
    this.canvasElements.push(canvas)
  }

  /**
   * 多层次水印绘制
   */
  private async drawMultiLayerWatermark(
    ctx: CanvasRenderingContext2D, 
    text: string, 
    width: number, 
    height: number
  ): Promise<void> {
    // 1. 像素级水印（抗压缩）
    this.drawPixelWatermark(ctx, text, width, height)
    
    // 2. 频域水印（抗干扰）
    this.drawFrequencyWatermark(ctx, text, width, height)
    
    // 3. 文本水印（可视化验证）
    this.drawTextWatermark(ctx, text, width, height)
  }

  /**
   * 像素级水印 - 在像素的最低位嵌入信息
   */
  private drawPixelWatermark(ctx: CanvasRenderingContext2D, text: string, width: number, height: number): void {
    const imageData = ctx.createImageData(width, height)
    const data = imageData.data
    
    // 将文本转换为二进制
    const binaryData = this.textToBinary(text)
    let bitIndex = 0
    
    // 在像素的最低位嵌入数据
    for (let i = 0; i < data.length; i += 4) {
      if (bitIndex < binaryData.length) {
        // 在红色通道的最低位嵌入数据
        const bit = parseInt(binaryData[bitIndex])
        data[i] = (data[i] & 0xFE) | bit // 清除最低位并设置新值
        bitIndex++
      }
      
      // 设置透明度
      data[i + 3] = Math.floor(this.config.strength * 255)
    }
    
    ctx.putImageData(imageData, 0, 0)
  }

  /**
   * 频域水印 - 在频域中嵌入信息
   */
  private drawFrequencyWatermark(ctx: CanvasRenderingContext2D, text: string, width: number, height: number): void {
    // 创建规律性的频域模式
    const pattern = this.generateFrequencyPattern(text)
    
    for (let i = 0; i < pattern.length; i++) {
      const x = (i * 37) % width // 使用质数分布
      const y = (i * 41) % height
      const intensity = pattern[i] * this.config.strength * 255
      
      ctx.fillStyle = `rgba(${intensity}, ${intensity}, ${intensity}, ${this.config.strength})`
      ctx.fillRect(x, y, 2, 2)
    }
  }

  /**
   * 文本水印 - 可视化验证用
   */
  private drawTextWatermark(ctx: CanvasRenderingContext2D, text: string, width: number, height: number): void {
    // 使用更大的字体和更高的透明度
    ctx.font = 'bold 16px Arial'
    ctx.fillStyle = `rgba(0, 0, 0, ${Math.min(this.config.strength * 8, 0.3)})` // 大幅提高可见性

    const displayText = `${text} | 验证码: ${this.generateVerificationCode(text)}`

    // 更密集的重复绘制
    for (let y = 30; y < height; y += 80) {
      for (let x = 30; x < width; x += 250) {
        ctx.save()
        ctx.translate(x, y)
        ctx.rotate(-Math.PI / 6) // 旋转30度

        // 绘制文本阴影增强可见性
        ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(this.config.strength * 4, 0.15)})`
        ctx.fillText(displayText, 1, 1)

        // 绘制主文本
        ctx.fillStyle = `rgba(0, 0, 0, ${Math.min(this.config.strength * 8, 0.3)})`
        ctx.fillText(displayText, 0, 0)

        ctx.restore()
      }
    }
  }

  /**
   * 创建CSS水印（兼容性保障）
   */
  private async createCSSWatermark(data: WatermarkData): Promise<void> {
    const element = document.createElement('div')
    const watermarkText = this.encodeWatermarkData(data)

    element.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9998;
      background: ${this.generateCSSPattern(watermarkText)};
      opacity: ${Math.min(this.config.strength * 2, 0.4)};
      mix-blend-mode: overlay;
    `

    // 添加抗压缩的文本层
    const textLayer = this.createAntiCompressionTextLayer(data)
    element.appendChild(textLayer)

    this.container!.appendChild(element)
    this.watermarkElements.push(element)
  }

  /**
   * 创建抗压缩文本层
   */
  private createAntiCompressionTextLayer(data: WatermarkData): HTMLElement {
    const textElement = document.createElement('div')
    const watermarkText = this.encodeWatermarkData(data)
    const displayText = `${watermarkText} | 验证: ${this.generateVerificationCode(watermarkText)}`

    textElement.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      font-family: 'Arial Black', Arial, sans-serif;
      font-size: 14px;
      font-weight: bold;
      color: rgba(0, 0, 0, ${Math.min(this.config.strength * 6, 0.25)});
      text-shadow: 1px 1px 1px rgba(255,255,255,0.3), -1px -1px 1px rgba(255,255,255,0.3);
      white-space: nowrap;
      overflow: hidden;
      pointer-events: none;
      transform: rotate(-30deg);
      transform-origin: center;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      line-height: 60px;
      z-index: 1;
    `

    // 创建更密集的文本重复
    for (let i = 0; i < 50; i++) {
      const span = document.createElement('span')
      span.textContent = displayText
      span.style.cssText = `
        margin-right: 120px;
        opacity: 0.9;
        letter-spacing: 1px;
      `
      textElement.appendChild(span)
    }

    return textElement
  }

  /**
   * 创建DOM水印（备用方案）
   */
  private async createDOMWatermark(data: WatermarkData): Promise<void> {
    const element = document.createElement('div')
    const watermarkText = this.encodeWatermarkData(data)
    
    element.innerHTML = this.generateDOMPattern(watermarkText)
    element.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9997;
      opacity: ${this.config.strength * 0.5};
      overflow: hidden;
    `
    
    this.container!.appendChild(element)
    this.watermarkElements.push(element)
  }

  /**
   * 编码水印数据
   */
  private encodeWatermarkData(data: WatermarkData): string {
    const encoded = `${data.user}|${data.timestamp}|${data.path}|${data.sessionId}`
    return this.config.debugMode ? encoded : this.obfuscateData(encoded)
  }

  /**
   * 数据混淆
   */
  private obfuscateData(data: string): string {
    // 简单的Base64编码 + 字符替换
    const base64 = btoa(data)
    return base64.replace(/[+/=]/g, (match) => {
      switch (match) {
        case '+': return '-'
        case '/': return '_'
        case '=': return '.'
        default: return match
      }
    })
  }

  /**
   * 文本转二进制
   */
  private textToBinary(text: string): string {
    return text.split('').map(char => 
      char.charCodeAt(0).toString(2).padStart(8, '0')
    ).join('')
  }

  /**
   * 生成频域模式
   */
  private generateFrequencyPattern(text: string): number[] {
    const pattern: number[] = []
    const hash = this.simpleHash(text)
    
    for (let i = 0; i < 100; i++) {
      pattern.push(((hash + i) % 256) / 255)
    }
    
    return pattern
  }

  /**
   * 生成CSS模式
   */
  private generateCSSPattern(text: string): string {
    const hash = this.simpleHash(text)
    const colors = []
    
    for (let i = 0; i < 3; i++) {
      const r = (hash + i * 37) % 256
      const g = (hash + i * 41) % 256
      const b = (hash + i * 43) % 256
      colors.push(`rgba(${r}, ${g}, ${b}, ${this.config.strength})`)
    }
    
    return `
      radial-gradient(circle at 25% 25%, ${colors[0]} 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, ${colors[1]} 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, ${colors[2]} 0%, transparent 50%)
    `
  }

  /**
   * 生成DOM模式
   */
  private generateDOMPattern(text: string): string {
    const displayText = text.length > 50 ? text.substring(0, 50) + '...' : text
    let html = ''
    
    for (let i = 0; i < 20; i++) {
      html += `<div style="
        position: absolute;
        top: ${(i * 5) % 100}%;
        left: ${(i * 7) % 100}%;
        font-size: 10px;
        color: rgba(0,0,0,0.1);
        transform: rotate(${(i * 15) % 360}deg);
        white-space: nowrap;
      ">${displayText}</div>`
    }
    
    return html
  }

  /**
   * 生成验证码
   */
  private generateVerificationCode(text: string): string {
    const hash = this.simpleHash(text)
    return (hash % 10000).toString().padStart(4, '0')
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 销毁水印
   */
  destroy(): void {
    this.watermarkElements.forEach(el => el.remove())
    this.canvasElements.forEach(canvas => canvas.remove())
    this.watermarkElements = []
    this.canvasElements = []
    this.isActive = false
  }

  /**
   * 获取状态
   */
  getStatus(): { active: boolean, elements: number } {
    return {
      active: this.isActive,
      elements: this.watermarkElements.length + this.canvasElements.length
    }
  }

  /**
   * 日志输出
   */
  private log(message: string, data?: any): void {
    if (this.config.debugMode) {
      console.log(`[WatermarkSystem] ${message}`, data || '')
    }
  }
}
