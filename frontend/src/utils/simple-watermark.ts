/**
 * 简化版暗水印系统
 * 专注于兼容性和稳定性
 */

export interface SimpleWatermarkConfig {
  /** 水印强度 */
  strength: number
  /** 刷新间隔 */
  refreshInterval: number
  /** 是否启用 */
  enabled: boolean
  /** 水印图案大小 */
  patternSize: number
  /** 噪点密度 */
  noiseDensity: number
  /** 文本大小 */
  textSize: number
}

export interface SimpleWatermarkData {
  user: string
  timestamp: number
  path: string
  params: Record<string, any>
}

export class SimpleWatermark {
  private config: SimpleWatermarkConfig
  private container: HTMLElement
  private watermarkElement: HTMLElement | null = null
  private refreshTimer?: number
  private isDestroyed = false

  constructor(container: HTMLElement = document.body, config: Partial<SimpleWatermarkConfig> = {}) {
    this.container = container
    this.config = {
      strength: 0.02, // 增强默认强度
      refreshInterval: 60000,
      enabled: true,
      patternSize: 300, // 增大图案尺寸
      noiseDensity: 50, // 噪点密度
      textSize: 14, // 文本大小
      ...config
    }
  }

  /**
   * 启动水印
   */
  async start(data: SimpleWatermarkData): Promise<void> {
    if (this.isDestroyed || !this.config.enabled) return

    try {
      // 清理现有水印
      this.stop()

      // 创建水印元素
      await this.createWatermarkElement(data)

      // 启动定时刷新
      this.startRefreshTimer(data)

      console.log('简化版水印系统启动成功')
    } catch (error) {
      console.error('简化版水印系统启动失败:', error)
      throw error
    }
  }

  /**
   * 停止水印
   */
  stop(): void {
    // 清理定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = undefined
    }

    // 移除水印元素
    if (this.watermarkElement && this.watermarkElement.parentNode) {
      this.watermarkElement.parentNode.removeChild(this.watermarkElement)
      this.watermarkElement = null
    }
  }

  /**
   * 刷新水印
   */
  async refresh(data: SimpleWatermarkData): Promise<void> {
    if (this.isDestroyed || !this.config.enabled) return
    await this.start(data)
  }

  /**
   * 创建水印元素
   */
  private async createWatermarkElement(data: SimpleWatermarkData): Promise<void> {
    // 创建水印容器
    const watermarkDiv = document.createElement('div')
    watermarkDiv.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9999;
      opacity: ${this.config.strength * 50}; // 调整透明度计算
      mix-blend-mode: multiply;
    `

    // 生成水印图案
    const pattern = await this.generateWatermarkPattern(data)
    watermarkDiv.style.backgroundImage = `url(${pattern})`
    watermarkDiv.style.backgroundRepeat = 'repeat'

    // 添加到容器
    this.container.appendChild(watermarkDiv)
    this.watermarkElement = watermarkDiv
  }

  /**
   * 生成水印图案
   */
  private async generateWatermarkPattern(data: SimpleWatermarkData): Promise<string> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    // 使用配置的图案大小
    const size = this.config.patternSize
    canvas.width = size
    canvas.height = size

    // 设置白色背景（便于观察）
    ctx.fillStyle = 'rgba(255, 255, 255, 0.01)'
    ctx.fillRect(0, 0, size, size)

    // 生成基于数据的哈希
    const hash = this.generateHash(JSON.stringify(data))

    // 绘制增强的噪点图案
    this.drawEnhancedNoisePattern(ctx, hash, size)

    // 添加增强的文本信息
    this.drawEnhancedTextInfo(ctx, data, size)

    // 添加边框标识
    this.drawBorderMarkers(ctx, size)

    return canvas.toDataURL('image/png')
  }

  /**
   * 生成哈希值
   */
  private generateHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 绘制增强的噪点图案
   */
  private drawEnhancedNoisePattern(ctx: CanvasRenderingContext2D, hash: number, size: number): void {
    const alpha = this.config.strength
    const density = this.config.noiseDensity

    // 绘制特征点阵列（更规律，便于识别）
    this.drawFeatureMatrix(ctx, hash, size, alpha)

    // 生成伪随机噪点
    let seed = hash
    for (let i = 0; i < density; i++) {
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const x = (seed % size)
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const y = (seed % size)
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const radius = 1 + (seed % 2)

      // 使用更强的对比度
      ctx.fillStyle = `rgba(0, 0, 0, ${alpha * 3})`
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, 2 * Math.PI)
      ctx.fill()
    }

    // 添加网格图案增强识别
    this.drawGridPattern(ctx, hash, size, alpha)

    // 添加同心圆图案
    this.drawConcentricCircles(ctx, hash, size, alpha)
  }

  /**
   * 绘制特征点阵列
   */
  private drawFeatureMatrix(ctx: CanvasRenderingContext2D, hash: number, size: number, alpha: number): void {
    const gridSize = 20
    const dotSize = 2

    // 基于哈希生成特征模式
    const pattern = this.hashToPattern(hash, 16) // 4x4 特征矩阵

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        if (pattern[row * 4 + col]) {
          const x = (col + 1) * (size / 5) - dotSize
          const y = (row + 1) * (size / 5) - dotSize

          ctx.fillStyle = `rgba(0, 0, 0, ${alpha * 4})`
          ctx.fillRect(x, y, dotSize * 2, dotSize * 2)
        }
      }
    }
  }

  /**
   * 哈希转特征模式
   */
  private hashToPattern(hash: number, length: number): boolean[] {
    const pattern: boolean[] = []
    let seed = hash

    for (let i = 0; i < length; i++) {
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      pattern.push((seed % 3) === 0) // 约1/3的点为true
    }

    return pattern
  }

  /**
   * 绘制同心圆图案
   */
  private drawConcentricCircles(ctx: CanvasRenderingContext2D, hash: number, size: number, alpha: number): void {
    const centerX = size / 2
    const centerY = size / 2
    const maxRadius = size / 6

    ctx.strokeStyle = `rgba(0, 0, 0, ${alpha * 2})`
    ctx.lineWidth = 1

    // 基于哈希决定圆的数量
    const circleCount = 2 + (hash % 3)

    for (let i = 1; i <= circleCount; i++) {
      const radius = (maxRadius / circleCount) * i
      ctx.beginPath()
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
      ctx.stroke()
    }
  }

  /**
   * 绘制网格图案
   */
  private drawGridPattern(ctx: CanvasRenderingContext2D, hash: number, size: number, alpha: number): void {
    const gridSize = 20
    const lineWidth = 1

    ctx.strokeStyle = `rgba(0, 0, 0, ${alpha * 0.5})`
    ctx.lineWidth = lineWidth

    // 基于哈希决定网格偏移
    const offsetX = hash % gridSize
    const offsetY = (hash >> 8) % gridSize

    // 绘制垂直线
    for (let x = offsetX; x < size; x += gridSize) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, size)
      ctx.stroke()
    }

    // 绘制水平线
    for (let y = offsetY; y < size; y += gridSize) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(size, y)
      ctx.stroke()
    }
  }

  /**
   * 绘制增强的文本信息
   */
  private drawEnhancedTextInfo(ctx: CanvasRenderingContext2D, data: SimpleWatermarkData, size: number): void {
    const alpha = this.config.strength
    const fontSize = this.config.textSize

    // 编码完整信息为JSON字符串
    const watermarkInfo = {
      u: data.user,
      t: data.timestamp,
      p: data.path,
      r: Object.keys(data.params).length > 0 ? data.params : null
    }
    const infoString = JSON.stringify(watermarkInfo)

    // 设置增强的字体样式
    ctx.font = `${fontSize}px monospace`
    ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`
    ctx.textAlign = 'left'

    // 将信息分行绘制，增加行数
    const maxCharsPerLine = Math.floor(size / (fontSize * 0.6))
    const lines = this.splitTextToLines(infoString, maxCharsPerLine)
    const lineHeight = fontSize + 2
    const startY = 30

    lines.forEach((line, index) => {
      // 绘制文本阴影增强可见性
      ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.3})`
      ctx.fillText(line, 11, startY + index * lineHeight + 1)

      // 绘制主文本
      ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`
      ctx.fillText(line, 10, startY + index * lineHeight)
    })

    // 在多个位置绘制标识
    ctx.font = `${fontSize - 2}px Arial`
    ctx.textAlign = 'right'
    ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`
    ctx.fillText(`WM:${data.user}`, size - 10, size - 10)

    ctx.textAlign = 'left'
    ctx.fillText(`T:${new Date(data.timestamp).toLocaleTimeString()}`, 10, size - 10)
  }

  /**
   * 绘制边框标识
   */
  private drawBorderMarkers(ctx: CanvasRenderingContext2D, size: number): void {
    const alpha = this.config.strength
    const markerSize = 5

    ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`

    // 四个角落的标识
    const positions = [
      [0, 0], [size - markerSize, 0],
      [0, size - markerSize], [size - markerSize, size - markerSize]
    ]

    positions.forEach(([x, y]) => {
      ctx.fillRect(x, y, markerSize, markerSize)
    })

    // 边框中点标识
    const midX = size / 2 - markerSize / 2
    const midY = size / 2 - markerSize / 2

    ctx.fillRect(midX, 0, markerSize, markerSize) // 上
    ctx.fillRect(midX, size - markerSize, markerSize, markerSize) // 下
    ctx.fillRect(0, midY, markerSize, markerSize) // 左
    ctx.fillRect(size - markerSize, midY, markerSize, markerSize) // 右
  }

  /**
   * 将文本分割为指定长度的行
   */
  private splitTextToLines(text: string, maxLength: number): string[] {
    const lines: string[] = []
    for (let i = 0; i < text.length; i += maxLength) {
      lines.push(text.substring(i, i + maxLength))
    }
    return lines
  }

  /**
   * 启动刷新定时器
   */
  private startRefreshTimer(data: SimpleWatermarkData): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }

    this.refreshTimer = window.setInterval(() => {
      if (this.isDestroyed) return

      const updatedData = {
        ...data,
        timestamp: Date.now()
      }
      
      this.refresh(updatedData).catch(error => {
        console.error('水印刷新失败:', error)
      })
    }, this.config.refreshInterval)
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SimpleWatermarkConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 销毁水印
   */
  destroy(): void {
    this.isDestroyed = true
    this.stop()
  }
}

/**
 * Vue3 组合式函数
 */
export function useSimpleWatermark(container?: HTMLElement, config?: Partial<SimpleWatermarkConfig>) {
  let watermark: SimpleWatermark | null = null
  
  const start = async (data: SimpleWatermarkData) => {
    if (!watermark) {
      watermark = new SimpleWatermark(container, config)
    }
    await watermark.start(data)
  }

  const stop = () => {
    if (watermark) {
      watermark.stop()
    }
  }

  const refresh = async (data: SimpleWatermarkData) => {
    if (watermark) {
      await watermark.refresh(data)
    }
  }

  const destroy = () => {
    if (watermark) {
      watermark.destroy()
      watermark = null
    }
  }

  const updateConfig = (newConfig: Partial<SimpleWatermarkConfig>) => {
    if (watermark) {
      watermark.updateConfig(newConfig)
    }
  }

  return {
    start,
    stop,
    refresh,
    destroy,
    updateConfig
  }
}

/**
 * 检查浏览器兼容性
 */
export function checkSimpleWatermarkCompatibility(): boolean {
  try {
    // 检查基本的Canvas支持
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) return false

    // 检查基本的CSS支持
    const testDiv = document.createElement('div')
    testDiv.style.mixBlendMode = 'multiply'
    
    return true
  } catch (error) {
    console.warn('浏览器兼容性检查失败:', error)
    return false
  }
}
