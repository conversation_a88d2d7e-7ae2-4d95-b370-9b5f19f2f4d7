/**
 * 简化版暗水印系统
 * 专注于兼容性和稳定性
 */

export interface SimpleWatermarkConfig {
  /** 水印强度 */
  strength: number
  /** 刷新间隔 */
  refreshInterval: number
  /** 是否启用 */
  enabled: boolean
}

export interface SimpleWatermarkData {
  user: string
  timestamp: number
  path: string
  params: Record<string, any>
}

export class SimpleWatermark {
  private config: SimpleWatermarkConfig
  private container: HTMLElement
  private watermarkElement: HTMLElement | null = null
  private refreshTimer?: number
  private isDestroyed = false

  constructor(container: HTMLElement = document.body, config: Partial<SimpleWatermarkConfig> = {}) {
    this.container = container
    this.config = {
      strength: 0.005,
      refreshInterval: 60000,
      enabled: true,
      ...config
    }
  }

  /**
   * 启动水印
   */
  async start(data: SimpleWatermarkData): Promise<void> {
    if (this.isDestroyed || !this.config.enabled) return

    try {
      // 清理现有水印
      this.stop()

      // 创建水印元素
      await this.createWatermarkElement(data)

      // 启动定时刷新
      this.startRefreshTimer(data)

      console.log('简化版水印系统启动成功')
    } catch (error) {
      console.error('简化版水印系统启动失败:', error)
      throw error
    }
  }

  /**
   * 停止水印
   */
  stop(): void {
    // 清理定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = undefined
    }

    // 移除水印元素
    if (this.watermarkElement && this.watermarkElement.parentNode) {
      this.watermarkElement.parentNode.removeChild(this.watermarkElement)
      this.watermarkElement = null
    }
  }

  /**
   * 刷新水印
   */
  async refresh(data: SimpleWatermarkData): Promise<void> {
    if (this.isDestroyed || !this.config.enabled) return
    await this.start(data)
  }

  /**
   * 创建水印元素
   */
  private async createWatermarkElement(data: SimpleWatermarkData): Promise<void> {
    // 创建水印容器
    const watermarkDiv = document.createElement('div')
    watermarkDiv.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9999;
      opacity: ${this.config.strength * 200};
      mix-blend-mode: multiply;
    `

    // 生成水印图案
    const pattern = await this.generateWatermarkPattern(data)
    watermarkDiv.style.backgroundImage = `url(${pattern})`
    watermarkDiv.style.backgroundRepeat = 'repeat'

    // 添加到容器
    this.container.appendChild(watermarkDiv)
    this.watermarkElement = watermarkDiv
  }

  /**
   * 生成水印图案
   */
  private async generateWatermarkPattern(data: SimpleWatermarkData): Promise<string> {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    // 设置画布大小
    const size = 200
    canvas.width = size
    canvas.height = size

    // 清空画布
    ctx.fillStyle = 'transparent'
    ctx.fillRect(0, 0, size, size)

    // 生成基于数据的哈希
    const hash = this.generateHash(JSON.stringify(data))
    
    // 绘制基于哈希的图案
    this.drawHashPattern(ctx, hash, size)

    // 添加文本信息
    this.drawTextInfo(ctx, data, size)

    return canvas.toDataURL('image/png')
  }

  /**
   * 生成哈希值
   */
  private generateHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 绘制基于哈希的图案
   */
  private drawHashPattern(ctx: CanvasRenderingContext2D, hash: number, size: number): void {
    const alpha = this.config.strength
    
    // 使用哈希生成伪随机点
    let seed = hash
    for (let i = 0; i < 20; i++) {
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const x = (seed % size)
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const y = (seed % size)
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const radius = 1 + (seed % 3)

      ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, 2 * Math.PI)
      ctx.fill()
    }
  }

  /**
   * 绘制文本信息
   */
  private drawTextInfo(ctx: CanvasRenderingContext2D, data: SimpleWatermarkData, size: number): void {
    const alpha = this.config.strength * 0.8

    // 编码完整信息为JSON字符串
    const watermarkInfo = {
      u: data.user,
      t: data.timestamp,
      p: data.path,
      r: Object.keys(data.params).length > 0 ? data.params : null
    }
    const infoString = JSON.stringify(watermarkInfo)

    // 设置字体样式
    ctx.font = '10px monospace'
    ctx.fillStyle = `rgba(0, 0, 0, ${alpha})`
    ctx.textAlign = 'left'

    // 将信息分行绘制
    const lines = this.splitTextToLines(infoString, 18) // 每行最多18个字符
    const lineHeight = 12
    const startY = 20

    lines.forEach((line, index) => {
      ctx.fillText(line, 10, startY + index * lineHeight)
    })

    // 在右下角绘制简化标识
    ctx.font = '8px Arial'
    ctx.textAlign = 'right'
    ctx.fillText(`WM:${data.user}`, size - 10, size - 10)
  }

  /**
   * 将文本分割为指定长度的行
   */
  private splitTextToLines(text: string, maxLength: number): string[] {
    const lines: string[] = []
    for (let i = 0; i < text.length; i += maxLength) {
      lines.push(text.substring(i, i + maxLength))
    }
    return lines
  }

  /**
   * 启动刷新定时器
   */
  private startRefreshTimer(data: SimpleWatermarkData): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }

    this.refreshTimer = window.setInterval(() => {
      if (this.isDestroyed) return

      const updatedData = {
        ...data,
        timestamp: Date.now()
      }
      
      this.refresh(updatedData).catch(error => {
        console.error('水印刷新失败:', error)
      })
    }, this.config.refreshInterval)
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SimpleWatermarkConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 销毁水印
   */
  destroy(): void {
    this.isDestroyed = true
    this.stop()
  }
}

/**
 * Vue3 组合式函数
 */
export function useSimpleWatermark(container?: HTMLElement, config?: Partial<SimpleWatermarkConfig>) {
  let watermark: SimpleWatermark | null = null
  
  const start = async (data: SimpleWatermarkData) => {
    if (!watermark) {
      watermark = new SimpleWatermark(container, config)
    }
    await watermark.start(data)
  }

  const stop = () => {
    if (watermark) {
      watermark.stop()
    }
  }

  const refresh = async (data: SimpleWatermarkData) => {
    if (watermark) {
      await watermark.refresh(data)
    }
  }

  const destroy = () => {
    if (watermark) {
      watermark.destroy()
      watermark = null
    }
  }

  const updateConfig = (newConfig: Partial<SimpleWatermarkConfig>) => {
    if (watermark) {
      watermark.updateConfig(newConfig)
    }
  }

  return {
    start,
    stop,
    refresh,
    destroy,
    updateConfig
  }
}

/**
 * 检查浏览器兼容性
 */
export function checkSimpleWatermarkCompatibility(): boolean {
  try {
    // 检查基本的Canvas支持
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) return false

    // 检查基本的CSS支持
    const testDiv = document.createElement('div')
    testDiv.style.mixBlendMode = 'multiply'
    
    return true
  } catch (error) {
    console.warn('浏览器兼容性检查失败:', error)
    return false
  }
}
