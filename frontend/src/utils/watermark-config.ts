/**
 * 水印配置管理
 * 提供不同环境和场景下的水印配置
 */

import { WatermarkTechnique } from './watermark-composer'

export interface WatermarkProfile {
  name: string
  description: string
  techniques: WatermarkTechnique[]
  strength: number
  refreshInterval: number
  enabled: boolean
}

/**
 * 预定义的水印配置档案
 */
export const WATERMARK_PROFILES: Record<string, WatermarkProfile> = {
  // 高安全级别 - 适用于敏感数据页面
  high_security: {
    name: '高安全',
    description: '多重水印技术，强抗干扰能力',
    techniques: ['frequency', 'css', 'svg'],
    strength: 0.002,
    refreshInterval: 30000, // 30秒
    enabled: true
  },

  // 标准级别 - 适用于一般业务页面
  standard: {
    name: '标准',
    description: '平衡性能与安全性',
    techniques: ['frequency', 'css'],
    strength: 0.001,
    refreshInterval: 60000, // 1分钟
    enabled: true
  },

  // 轻量级别 - 适用于性能敏感页面
  lightweight: {
    name: '轻量',
    description: '最小性能影响',
    techniques: ['css'],
    strength: 0.0005,
    refreshInterval: 120000, // 2分钟
    enabled: true
  },

  // 实验性 - 包含WebGL等新技术
  experimental: {
    name: '实验性',
    description: '包含最新水印技术',
    techniques: ['frequency', 'css', 'svg', 'webgl'],
    strength: 0.006,
    refreshInterval: 45000, // 45秒
    enabled: false // 默认关闭
  },

  // 开发模式 - 用于开发调试
  development: {
    name: '开发模式',
    description: '开发调试专用',
    techniques: ['css'],
    strength: 0.01, // 更明显的水印便于调试
    refreshInterval: 10000, // 10秒快速刷新
    enabled: true
  }
}

/**
 * 浏览器兼容性检测
 */
export function detectBrowserCapabilities() {
  const capabilities = {
    canvas: false,
    webgl: false,
    css3: false,
    svg: false,
    imageData: false,
    mixBlendMode: false,
    performance: false
  }

  try {
    // Canvas支持
    const canvas = document.createElement('canvas')
    capabilities.canvas = !!canvas.getContext('2d')
    
    // WebGL支持
    capabilities.webgl = !!canvas.getContext('webgl') || !!canvas.getContext('experimental-webgl')
    
    // ImageData支持
    if (capabilities.canvas) {
      const ctx = canvas.getContext('2d')
      capabilities.imageData = !!(ctx && ctx.createImageData)
    }
    
    // CSS3支持
    capabilities.css3 = CSS.supports('transform', 'translateZ(0)')
    
    // Mix-blend-mode支持
    capabilities.mixBlendMode = CSS.supports('mix-blend-mode', 'multiply')
    
    // SVG支持
    capabilities.svg = !!document.createElementNS && 
      !!document.createElementNS('http://www.w3.org/2000/svg', 'svg').createSVGRect
    
    // Performance API支持
    capabilities.performance = !!(window.performance && window.performance.memory)
    
  } catch (error) {
    console.warn('浏览器能力检测失败:', error)
  }

  return capabilities
}

/**
 * 根据浏览器能力推荐配置
 */
export function recommendProfile(): string {
  const capabilities = detectBrowserCapabilities()
  
  // 检查是否为移动设备
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  
  // 检查是否为开发环境
  const isDevelopment = import.meta.env.MODE === 'development'
  
  if (isDevelopment) {
    return 'development'
  }
  
  if (isMobile) {
    return 'lightweight' // 移动设备使用轻量级
  }
  
  // 根据浏览器能力推荐
  if (capabilities.canvas && capabilities.imageData && capabilities.mixBlendMode) {
    if (capabilities.webgl && capabilities.svg) {
      return 'high_security' // 全功能支持
    }
    return 'standard' // 标准支持
  }
  
  if (capabilities.css3) {
    return 'lightweight' // 基础CSS支持
  }
  
  // 兜底方案
  return 'lightweight'
}

/**
 * 获取当前环境的默认配置
 */
export function getDefaultProfile(): WatermarkProfile {
  const profileName = recommendProfile()
  return WATERMARK_PROFILES[profileName] || WATERMARK_PROFILES.standard
}

/**
 * 验证配置有效性
 */
export function validateProfile(profile: Partial<WatermarkProfile>): boolean {
  if (!profile.techniques || profile.techniques.length === 0) {
    return false
  }
  
  if (typeof profile.strength !== 'number' || profile.strength <= 0 || profile.strength > 1) {
    return false
  }
  
  if (typeof profile.refreshInterval !== 'number' || profile.refreshInterval < 1000) {
    return false
  }
  
  return true
}

/**
 * 合并配置
 */
export function mergeProfiles(base: WatermarkProfile, override: Partial<WatermarkProfile>): WatermarkProfile {
  return {
    ...base,
    ...override,
    techniques: override.techniques || base.techniques
  }
}

/**
 * 配置管理器
 */
export class WatermarkConfigManager {
  private currentProfile: WatermarkProfile
  private customProfiles: Map<string, WatermarkProfile> = new Map()
  
  constructor() {
    this.currentProfile = getDefaultProfile()
    this.loadCustomProfiles()
  }
  
  /**
   * 获取当前配置
   */
  getCurrentProfile(): WatermarkProfile {
    return { ...this.currentProfile }
  }
  
  /**
   * 设置配置档案
   */
  setProfile(profileName: string): boolean {
    const profile = WATERMARK_PROFILES[profileName] || this.customProfiles.get(profileName)
    
    if (!profile) {
      console.warn(`配置档案 ${profileName} 不存在`)
      return false
    }
    
    if (!validateProfile(profile)) {
      console.warn(`配置档案 ${profileName} 无效`)
      return false
    }
    
    this.currentProfile = { ...profile }
    this.saveCurrentProfile()
    return true
  }
  
  /**
   * 更新当前配置
   */
  updateProfile(updates: Partial<WatermarkProfile>): boolean {
    const newProfile = mergeProfiles(this.currentProfile, updates)
    
    if (!validateProfile(newProfile)) {
      console.warn('配置更新无效')
      return false
    }
    
    this.currentProfile = newProfile
    this.saveCurrentProfile()
    return true
  }
  
  /**
   * 添加自定义配置
   */
  addCustomProfile(name: string, profile: WatermarkProfile): boolean {
    if (!validateProfile(profile)) {
      console.warn('自定义配置无效')
      return false
    }
    
    this.customProfiles.set(name, { ...profile })
    this.saveCustomProfiles()
    return true
  }
  
  /**
   * 获取所有可用配置
   */
  getAllProfiles(): Record<string, WatermarkProfile> {
    const all = { ...WATERMARK_PROFILES }
    
    for (const [name, profile] of this.customProfiles) {
      all[name] = profile
    }
    
    return all
  }
  
  /**
   * 重置为默认配置
   */
  reset(): void {
    this.currentProfile = getDefaultProfile()
    this.customProfiles.clear()
    this.saveCurrentProfile()
    this.saveCustomProfiles()
  }
  
  /**
   * 从本地存储加载自定义配置
   */
  private loadCustomProfiles(): void {
    try {
      const saved = localStorage.getItem('watermark_custom_profiles')
      if (saved) {
        const profiles = JSON.parse(saved)
        for (const [name, profile] of Object.entries(profiles)) {
          if (validateProfile(profile as WatermarkProfile)) {
            this.customProfiles.set(name, profile as WatermarkProfile)
          }
        }
      }
      
      // 加载当前配置
      const currentSaved = localStorage.getItem('watermark_current_profile')
      if (currentSaved) {
        const profile = JSON.parse(currentSaved)
        if (validateProfile(profile)) {
          this.currentProfile = profile
        }
      }
    } catch (error) {
      console.warn('加载水印配置失败:', error)
    }
  }
  
  /**
   * 保存当前配置到本地存储
   */
  private saveCurrentProfile(): void {
    try {
      localStorage.setItem('watermark_current_profile', JSON.stringify(this.currentProfile))
    } catch (error) {
      console.warn('保存当前水印配置失败:', error)
    }
  }
  
  /**
   * 保存自定义配置到本地存储
   */
  private saveCustomProfiles(): void {
    try {
      const profiles = Object.fromEntries(this.customProfiles)
      localStorage.setItem('watermark_custom_profiles', JSON.stringify(profiles))
    } catch (error) {
      console.warn('保存自定义水印配置失败:', error)
    }
  }
}

// 全局配置管理器实例
export const watermarkConfigManager = new WatermarkConfigManager()

/**
 * 获取性能优化建议
 */
export function getPerformanceRecommendations(): string[] {
  const capabilities = detectBrowserCapabilities()
  const recommendations: string[] = []
  
  if (!capabilities.canvas) {
    recommendations.push('浏览器不支持Canvas，建议升级浏览器')
  }
  
  if (!capabilities.webgl) {
    recommendations.push('浏览器不支持WebGL，无法使用高级水印技术')
  }
  
  if (!capabilities.mixBlendMode) {
    recommendations.push('浏览器不支持混合模式，水印效果可能受限')
  }
  
  if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
    recommendations.push('设备CPU核心数较少，建议使用轻量级配置')
  }
  
  if (navigator.deviceMemory && navigator.deviceMemory < 4) {
    recommendations.push('设备内存较少，建议降低水印刷新频率')
  }
  
  return recommendations
}
