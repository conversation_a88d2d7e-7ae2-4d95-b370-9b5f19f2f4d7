/**
 * 水印可视化工具
 * 用于在图像编辑软件中更容易观察到水印
 */

export interface VisualizationOptions {
  method: 'difference' | 'amplify' | 'edge' | 'frequency' | 'statistical'
  strength: number
  threshold: number
}

export class WatermarkVisualizer {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D

  constructor() {
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }

  /**
   * 处理图像以增强水印可见性
   */
  async processImageForVisualization(
    imageFile: File, 
    options: VisualizationOptions = {
      method: 'amplify',
      strength: 10,
      threshold: 5
    }
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        try {
          const result = this.enhanceWatermarkVisibility(img, options)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }
      img.onerror = () => reject(new Error('图像加载失败'))
      img.src = URL.createObjectURL(imageFile)
    })
  }

  /**
   * 增强水印可见性
   */
  private enhanceWatermarkVisibility(img: HTMLImageElement, options: VisualizationOptions): string {
    this.canvas.width = img.width
    this.canvas.height = img.height
    this.ctx.drawImage(img, 0, 0)

    const imageData = this.ctx.getImageData(0, 0, img.width, img.height)
    
    switch (options.method) {
      case 'amplify':
        this.amplifyMethod(imageData, options)
        break
      case 'difference':
        this.differenceMethod(imageData, options)
        break
      case 'edge':
        this.edgeDetectionMethod(imageData, options)
        break
      case 'frequency':
        this.frequencyAnalysisMethod(imageData, options)
        break
      case 'statistical':
        this.statisticalMethod(imageData, options)
        break
    }

    this.ctx.putImageData(imageData, 0, 0)
    return this.canvas.toDataURL('image/png')
  }

  /**
   * 放大法 - 放大微小的像素差异
   */
  private amplifyMethod(imageData: ImageData, options: VisualizationOptions): void {
    const { data, width, height } = imageData
    const originalData = new Uint8ClampedArray(data)
    
    for (let i = 0; i < data.length; i += 4) {
      const r = originalData[i]
      const g = originalData[i + 1]
      const b = originalData[i + 2]
      
      // 计算灰度值
      const gray = 0.299 * r + 0.587 * g + 0.114 * b
      
      // 计算与周围像素的差异
      const x = (i / 4) % width
      const y = Math.floor((i / 4) / width)
      
      if (x > 0 && x < width - 1 && y > 0 && y < height - 1) {
        let avgGray = 0
        let count = 0
        
        // 计算周围8个像素的平均灰度
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            if (dx === 0 && dy === 0) continue
            const idx = ((y + dy) * width + (x + dx)) * 4
            const neighborGray = 0.299 * originalData[idx] + 0.587 * originalData[idx + 1] + 0.114 * originalData[idx + 2]
            avgGray += neighborGray
            count++
          }
        }
        avgGray /= count
        
        // 放大差异
        const diff = gray - avgGray
        if (Math.abs(diff) > options.threshold) {
          const amplified = 128 + diff * options.strength
          const clampedValue = Math.max(0, Math.min(255, amplified))
          
          data[i] = clampedValue
          data[i + 1] = clampedValue
          data[i + 2] = clampedValue
        } else {
          // 保持原始灰度
          data[i] = gray
          data[i + 1] = gray
          data[i + 2] = gray
        }
      }
    }
  }

  /**
   * 差分法 - 与平滑版本对比
   */
  private differenceMethod(imageData: ImageData, options: VisualizationOptions): void {
    const { data, width, height } = imageData
    const originalData = new Uint8ClampedArray(data)
    
    // 创建平滑版本
    const smoothed = this.gaussianBlur(originalData, width, height, 2)
    
    for (let i = 0; i < data.length; i += 4) {
      const originalGray = 0.299 * originalData[i] + 0.587 * originalData[i + 1] + 0.114 * originalData[i + 2]
      const smoothedGray = 0.299 * smoothed[i] + 0.587 * smoothed[i + 1] + 0.114 * smoothed[i + 2]
      
      const diff = originalGray - smoothedGray
      const enhanced = 128 + diff * options.strength
      const clampedValue = Math.max(0, Math.min(255, enhanced))
      
      data[i] = clampedValue
      data[i + 1] = clampedValue
      data[i + 2] = clampedValue
    }
  }

  /**
   * 边缘检测法
   */
  private edgeDetectionMethod(imageData: ImageData, options: VisualizationOptions): void {
    const { data, width, height } = imageData
    const originalData = new Uint8ClampedArray(data)
    
    // Sobel算子
    const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1]
    const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1]
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let gx = 0, gy = 0
        
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * width + (x + kx)) * 4
            const gray = 0.299 * originalData[idx] + 0.587 * originalData[idx + 1] + 0.114 * originalData[idx + 2]
            
            const kernelIdx = (ky + 1) * 3 + (kx + 1)
            gx += gray * sobelX[kernelIdx]
            gy += gray * sobelY[kernelIdx]
          }
        }
        
        const magnitude = Math.sqrt(gx * gx + gy * gy)
        const enhanced = magnitude * options.strength
        const clampedValue = Math.max(0, Math.min(255, enhanced))
        
        const idx = (y * width + x) * 4
        data[idx] = clampedValue
        data[idx + 1] = clampedValue
        data[idx + 2] = clampedValue
      }
    }
  }

  /**
   * 频域分析法
   */
  private frequencyAnalysisMethod(imageData: ImageData, options: VisualizationOptions): void {
    const { data, width, height } = imageData
    
    // 转换为灰度
    const grayData: number[] = []
    for (let i = 0; i < data.length; i += 4) {
      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]
      grayData.push(gray)
    }
    
    // 简化的高频增强
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x
        const current = grayData[idx]
        
        // 计算拉普拉斯算子
        const laplacian = 
          -grayData[(y-1) * width + x] +
          -grayData[y * width + (x-1)] +
          4 * current +
          -grayData[y * width + (x+1)] +
          -grayData[(y+1) * width + x]
        
        const enhanced = current + laplacian * options.strength
        const clampedValue = Math.max(0, Math.min(255, enhanced))
        
        const pixelIdx = idx * 4
        data[pixelIdx] = clampedValue
        data[pixelIdx + 1] = clampedValue
        data[pixelIdx + 2] = clampedValue
      }
    }
  }

  /**
   * 统计分析法
   */
  private statisticalMethod(imageData: ImageData, options: VisualizationOptions): void {
    const { data, width, height } = imageData
    
    // 计算全局统计信息
    let sum = 0, sumSq = 0, count = 0
    for (let i = 0; i < data.length; i += 4) {
      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]
      sum += gray
      sumSq += gray * gray
      count++
    }
    
    const mean = sum / count
    const variance = (sumSq / count) - (mean * mean)
    const stdDev = Math.sqrt(variance)
    
    // 标准化并增强异常值
    for (let i = 0; i < data.length; i += 4) {
      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]
      const zScore = (gray - mean) / stdDev
      
      if (Math.abs(zScore) > options.threshold / 10) {
        const enhanced = 128 + zScore * options.strength * 20
        const clampedValue = Math.max(0, Math.min(255, enhanced))
        
        data[i] = clampedValue
        data[i + 1] = clampedValue
        data[i + 2] = clampedValue
      } else {
        data[i] = gray
        data[i + 1] = gray
        data[i + 2] = gray
      }
    }
  }

  /**
   * 高斯模糊
   */
  private gaussianBlur(data: Uint8ClampedArray, width: number, height: number, radius: number): Uint8ClampedArray {
    const result = new Uint8ClampedArray(data)
    
    // 简化的高斯模糊实现
    for (let y = radius; y < height - radius; y++) {
      for (let x = radius; x < width - radius; x++) {
        let r = 0, g = 0, b = 0, count = 0
        
        for (let dy = -radius; dy <= radius; dy++) {
          for (let dx = -radius; dx <= radius; dx++) {
            const idx = ((y + dy) * width + (x + dx)) * 4
            r += data[idx]
            g += data[idx + 1]
            b += data[idx + 2]
            count++
          }
        }
        
        const idx = (y * width + x) * 4
        result[idx] = r / count
        result[idx + 1] = g / count
        result[idx + 2] = b / count
      }
    }
    
    return result
  }

  /**
   * 生成Photoshop操作指南
   */
  generatePhotoshopGuide(): string {
    return `
# Photoshop中查看水印的方法

## 方法1：极端对比度调整
1. 打开图像
2. 图像 → 调整 → 亮度/对比度
3. 将对比度调到 +100
4. 将亮度调到 -50 到 +50 之间慢慢调整
5. 观察是否有规律性的点状或线状图案

## 方法2：色阶调整
1. 图像 → 调整 → 色阶
2. 将输入色阶的中间值（灰色三角）向左或向右大幅移动
3. 将输出色阶调整为 50-200 之间的范围
4. 观察图像中的异常区域

## 方法3：高通滤镜
1. 复制图层
2. 滤镜 → 其他 → 高通
3. 半径设置为 1-3 像素
4. 将混合模式改为"叠加"或"强光"
5. 调整不透明度到 50-100%

## 方法4：差值混合
1. 复制图层
2. 滤镜 → 模糊 → 高斯模糊（半径 2-5 像素）
3. 将混合模式改为"差值"
4. 调整不透明度观察效果

## 方法5：通道分析
1. 打开通道面板
2. 分别查看红、绿、蓝通道
3. 水印可能在某个特定通道中更明显
4. 可以复制通道并进行单独调整
    `
  }
}

// 导出便捷函数
export async function visualizeWatermark(file: File, method: VisualizationOptions['method'] = 'amplify'): Promise<string> {
  const visualizer = new WatermarkVisualizer()
  return await visualizer.processImageForVisualization(file, {
    method,
    strength: method === 'amplify' ? 20 : 10,
    threshold: 3
  })
}
