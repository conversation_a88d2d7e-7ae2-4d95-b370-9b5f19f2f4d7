/**
 * 水印信息提取工具
 * 从截图中提取暗水印信息
 */

export interface ExtractedWatermarkInfo {
  user: string
  timestamp: number
  path: string
  params: Record<string, any> | null
  extractedAt: number
  confidence: number // 提取置信度 0-1
}

export interface ExtractionResult {
  success: boolean
  data?: ExtractedWatermarkInfo
  error?: string
  rawText?: string
}

export class WatermarkExtractor {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D

  constructor() {
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }

  /**
   * 从图片文件提取水印信息
   */
  async extractFromFile(file: File): Promise<ExtractionResult> {
    try {
      const imageUrl = URL.createObjectURL(file)
      const result = await this.extractFromUrl(imageUrl)
      URL.revokeObjectURL(imageUrl)
      return result
    } catch (error) {
      return {
        success: false,
        error: `文件处理失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 从图片URL提取水印信息
   */
  async extractFromUrl(imageUrl: string): Promise<ExtractionResult> {
    return new Promise((resolve) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      img.onload = () => {
        try {
          const result = this.extractFromImage(img)
          resolve(result)
        } catch (error) {
          resolve({
            success: false,
            error: `图片处理失败: ${error instanceof Error ? error.message : '未知错误'}`
          })
        }
      }
      
      img.onerror = () => {
        resolve({
          success: false,
          error: '图片加载失败'
        })
      }
      
      img.src = imageUrl
    })
  }

  /**
   * 从Image对象提取水印信息
   */
  private extractFromImage(img: HTMLImageElement): ExtractionResult {
    // 设置画布大小
    this.canvas.width = img.width
    this.canvas.height = img.height
    
    // 绘制图片
    this.ctx.drawImage(img, 0, 0)
    
    // 获取图像数据
    const imageData = this.ctx.getImageData(0, 0, img.width, img.height)
    
    // 尝试多种提取方法
    const methods = [
      () => this.extractByPatternRecognition(imageData),
      () => this.extractByTextRecognition(imageData),
      () => this.extractByContrastEnhancement(imageData),
      () => this.extractByFrequencyAnalysis(imageData)
    ]
    
    for (const method of methods) {
      try {
        const result = method()
        if (result.success) {
          return result
        }
      } catch (error) {
        console.warn('提取方法失败:', error)
      }
    }
    
    return {
      success: false,
      error: '未能从图片中提取到水印信息'
    }
  }

  /**
   * 方法1: 图案识别提取
   */
  private extractByPatternRecognition(imageData: ImageData): ExtractionResult {
    const { width, height, data } = imageData
    
    // 寻找重复的水印图案
    const patternSize = 200 // 水印图案大小
    const patterns: Map<string, number> = new Map()
    
    // 扫描图像寻找重复图案
    for (let y = 0; y < height - patternSize; y += patternSize / 2) {
      for (let x = 0; x < width - patternSize; x += patternSize / 2) {
        const pattern = this.extractPattern(data, x, y, patternSize, width)
        const patternKey = this.patternToKey(pattern)
        patterns.set(patternKey, (patterns.get(patternKey) || 0) + 1)
      }
    }
    
    // 找到最频繁的图案
    let maxCount = 0
    let mostFrequentPattern = ''
    for (const [pattern, count] of patterns) {
      if (count > maxCount) {
        maxCount = count
        mostFrequentPattern = pattern
      }
    }
    
    if (maxCount >= 3) { // 至少出现3次才认为是水印
      const extractedText = this.decodePattern(mostFrequentPattern)
      return this.parseWatermarkText(extractedText, maxCount / 10)
    }
    
    return { success: false, error: '未找到重复图案' }
  }

  /**
   * 方法2: 文本识别提取
   */
  private extractByTextRecognition(imageData: ImageData): ExtractionResult {
    // 创建增强对比度的图像
    const enhancedData = this.enhanceContrast(imageData)
    
    // 寻找文本区域
    const textRegions = this.findTextRegions(enhancedData)
    
    for (const region of textRegions) {
      const text = this.extractTextFromRegion(enhancedData, region)
      if (text && this.isWatermarkText(text)) {
        return this.parseWatermarkText(text, 0.8)
      }
    }
    
    return { success: false, error: '未找到文本信息' }
  }

  /**
   * 方法3: 对比度增强提取
   */
  private extractByContrastEnhancement(imageData: ImageData): ExtractionResult {
    const enhanced = this.enhanceContrast(imageData, 3.0) // 大幅增强对比度
    
    // 转换为灰度并二值化
    const binary = this.toBinary(enhanced)
    
    // 寻找文本模式
    const text = this.recognizeTextFromBinary(binary)
    
    if (text && this.isWatermarkText(text)) {
      return this.parseWatermarkText(text, 0.7)
    }
    
    return { success: false, error: '对比度增强后未找到信息' }
  }

  /**
   * 方法4: 频域分析提取
   */
  private extractByFrequencyAnalysis(imageData: ImageData): ExtractionResult {
    // 简化的频域分析
    const lowFreqData = this.extractLowFrequency(imageData)
    const text = this.analyzeFrequencyPattern(lowFreqData)
    
    if (text && this.isWatermarkText(text)) {
      return this.parseWatermarkText(text, 0.6)
    }
    
    return { success: false, error: '频域分析未找到信息' }
  }

  /**
   * 提取图案
   */
  private extractPattern(data: Uint8ClampedArray, x: number, y: number, size: number, width: number): number[] {
    const pattern: number[] = []
    for (let py = 0; py < size && y + py < data.length / width / 4; py++) {
      for (let px = 0; px < size && x + px < width; px++) {
        const index = ((y + py) * width + (x + px)) * 4
        const gray = (data[index] + data[index + 1] + data[index + 2]) / 3
        pattern.push(Math.floor(gray))
      }
    }
    return pattern
  }

  /**
   * 图案转换为键值
   */
  private patternToKey(pattern: number[]): string {
    // 简化图案为特征值
    const features: number[] = []
    const step = Math.floor(pattern.length / 16) // 取16个特征点
    
    for (let i = 0; i < pattern.length; i += step) {
      features.push(pattern[i] || 0)
    }
    
    return features.join(',')
  }

  /**
   * 解码图案
   */
  private decodePattern(patternKey: string): string {
    // 这里应该实现图案到文本的解码
    // 简化实现：尝试从图案中提取可能的文本信息
    return patternKey.split(',').map(n => String.fromCharCode(parseInt(n) % 128)).join('')
  }

  /**
   * 增强对比度
   */
  private enhanceContrast(imageData: ImageData, factor: number = 2.0): ImageData {
    const enhanced = new ImageData(imageData.width, imageData.height)
    const data = imageData.data
    const newData = enhanced.data
    
    for (let i = 0; i < data.length; i += 4) {
      // 增强对比度
      newData[i] = Math.min(255, Math.max(0, (data[i] - 128) * factor + 128))     // R
      newData[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * factor + 128)) // G
      newData[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * factor + 128)) // B
      newData[i + 3] = data[i + 3] // A
    }
    
    return enhanced
  }

  /**
   * 寻找文本区域
   */
  private findTextRegions(imageData: ImageData): Array<{x: number, y: number, width: number, height: number}> {
    // 简化实现：返回一些可能的文本区域
    const regions = []
    const step = 50
    
    for (let y = 0; y < imageData.height - 100; y += step) {
      for (let x = 0; x < imageData.width - 200; x += step) {
        regions.push({ x, y, width: 200, height: 100 })
      }
    }
    
    return regions
  }

  /**
   * 从区域提取文本
   */
  private extractTextFromRegion(imageData: ImageData, region: {x: number, y: number, width: number, height: number}): string {
    // 简化的文本提取
    // 实际应用中可以使用OCR库如Tesseract.js
    return ''
  }

  /**
   * 检查是否为水印文本
   */
  private isWatermarkText(text: string): boolean {
    // 检查文本是否包含水印特征
    return text.includes('{"u":') || text.includes('"t":') || text.includes('WM:')
  }

  /**
   * 解析水印文本
   */
  private parseWatermarkText(text: string, confidence: number): ExtractionResult {
    try {
      // 尝试解析JSON格式的水印信息
      const jsonMatch = text.match(/\{"u":[^}]+\}/g)
      if (jsonMatch) {
        const watermarkData = JSON.parse(jsonMatch[0])
        return {
          success: true,
          data: {
            user: watermarkData.u,
            timestamp: watermarkData.t,
            path: watermarkData.p,
            params: watermarkData.r,
            extractedAt: Date.now(),
            confidence
          },
          rawText: text
        }
      }
      
      // 尝试解析简化格式
      const wmMatch = text.match(/WM:([^,\s]+)/g)
      if (wmMatch) {
        const user = wmMatch[0].replace('WM:', '')
        return {
          success: true,
          data: {
            user,
            timestamp: Date.now(),
            path: '未知',
            params: null,
            extractedAt: Date.now(),
            confidence: confidence * 0.5
          },
          rawText: text
        }
      }
      
      return { success: false, error: '无法解析水印格式' }
    } catch (error) {
      return { success: false, error: `解析失败: ${error instanceof Error ? error.message : '未知错误'}` }
    }
  }

  /**
   * 转换为二值图像
   */
  private toBinary(imageData: ImageData): ImageData {
    const binary = new ImageData(imageData.width, imageData.height)
    const threshold = 128
    
    for (let i = 0; i < imageData.data.length; i += 4) {
      const gray = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3
      const value = gray > threshold ? 255 : 0
      
      binary.data[i] = value
      binary.data[i + 1] = value
      binary.data[i + 2] = value
      binary.data[i + 3] = 255
    }
    
    return binary
  }

  /**
   * 从二值图像识别文本
   */
  private recognizeTextFromBinary(imageData: ImageData): string {
    // 简化的文本识别
    // 实际应用中需要更复杂的OCR算法
    return ''
  }

  /**
   * 提取低频信息
   */
  private extractLowFrequency(imageData: ImageData): ImageData {
    // 简化的低频提取
    return imageData
  }

  /**
   * 分析频域图案
   */
  private analyzeFrequencyPattern(imageData: ImageData): string {
    // 简化的频域分析
    return ''
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    this.canvas.width = 0
    this.canvas.height = 0
  }
}

/**
 * 便捷函数：从文件提取水印
 */
export async function extractWatermarkFromFile(file: File): Promise<ExtractionResult> {
  const extractor = new WatermarkExtractor()
  try {
    return await extractor.extractFromFile(file)
  } finally {
    extractor.destroy()
  }
}

/**
 * 便捷函数：从URL提取水印
 */
export async function extractWatermarkFromUrl(url: string): Promise<ExtractionResult> {
  const extractor = new WatermarkExtractor()
  try {
    return await extractor.extractFromUrl(url)
  } finally {
    extractor.destroy()
  }
}
