/**
 * 水印信息提取工具
 * 从截图中提取暗水印信息
 */

export interface ExtractedWatermarkInfo {
  user: string
  timestamp: number
  path: string
  params: Record<string, any> | null
  extractedAt: number
  confidence: number // 提取置信度 0-1
}

export interface ExtractionResult {
  success: boolean
  data?: ExtractedWatermarkInfo
  error?: string
  rawText?: string
}

export class WatermarkExtractor {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D

  constructor() {
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }

  /**
   * 从图片文件提取水印信息
   */
  async extractFromFile(file: File): Promise<ExtractionResult> {
    try {
      const imageUrl = URL.createObjectURL(file)
      const result = await this.extractFromUrl(imageUrl)
      URL.revokeObjectURL(imageUrl)
      return result
    } catch (error) {
      return {
        success: false,
        error: `文件处理失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 从图片URL提取水印信息
   */
  async extractFromUrl(imageUrl: string): Promise<ExtractionResult> {
    return new Promise((resolve) => {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      img.onload = () => {
        try {
          const result = this.extractFromImage(img)
          resolve(result)
        } catch (error) {
          resolve({
            success: false,
            error: `图片处理失败: ${error instanceof Error ? error.message : '未知错误'}`
          })
        }
      }
      
      img.onerror = () => {
        resolve({
          success: false,
          error: '图片加载失败'
        })
      }
      
      img.src = imageUrl
    })
  }

  /**
   * 从Image对象提取水印信息
   */
  private extractFromImage(img: HTMLImageElement): ExtractionResult {
    // 设置画布大小
    this.canvas.width = img.width
    this.canvas.height = img.height
    
    // 绘制图片
    this.ctx.drawImage(img, 0, 0)
    
    // 获取图像数据
    const imageData = this.ctx.getImageData(0, 0, img.width, img.height)
    
    // 尝试多种提取方法
    const methods = [
      () => this.extractByPatternRecognition(imageData),
      () => this.extractByTextRecognition(imageData),
      () => this.extractByContrastEnhancement(imageData),
      () => this.extractByFrequencyAnalysis(imageData)
    ]
    
    for (const method of methods) {
      try {
        const result = method()
        if (result.success) {
          return result
        }
      } catch (error) {
        console.warn('提取方法失败:', error)
      }
    }
    
    return {
      success: false,
      error: '未能从图片中提取到水印信息'
    }
  }

  /**
   * 方法1: 增强的图案识别提取
   */
  private extractByPatternRecognition(imageData: ImageData): ExtractionResult {
    const { width, height, data } = imageData

    // 寻找重复的水印图案，使用多种尺寸
    const patternSizes = [200, 250, 300, 350] // 多种可能的水印尺寸
    let bestResult: ExtractionResult = { success: false, error: '未找到图案' }
    let maxConfidence = 0

    for (const patternSize of patternSizes) {
      const result = this.findPatternsOfSize(data, width, height, patternSize)
      if (result.success && result.data && result.data.confidence > maxConfidence) {
        maxConfidence = result.data.confidence
        bestResult = result
      }
    }

    return bestResult
  }

  /**
   * 查找指定尺寸的图案
   */
  private findPatternsOfSize(data: Uint8ClampedArray, width: number, height: number, patternSize: number): ExtractionResult {
    const patterns: Map<string, { count: number, text: string }> = new Map()
    const step = Math.floor(patternSize / 3) // 重叠扫描

    // 扫描图像寻找重复图案
    for (let y = 0; y < height - patternSize; y += step) {
      for (let x = 0; x < width - patternSize; x += step) {
        const pattern = this.extractEnhancedPattern(data, x, y, patternSize, width)
        const patternKey = this.patternToKey(pattern.features)
        const extractedText = this.extractTextFromPattern(data, x, y, patternSize, width)

        if (extractedText && this.isWatermarkText(extractedText)) {
          const existing = patterns.get(patternKey) || { count: 0, text: '' }
          patterns.set(patternKey, {
            count: existing.count + 1,
            text: extractedText
          })
        }
      }
    }

    // 找到最频繁的有效图案
    let maxCount = 0
    let bestText = ''
    for (const [, info] of patterns) {
      if (info.count > maxCount && info.text) {
        maxCount = info.count
        bestText = info.text
      }
    }

    if (maxCount >= 2 && bestText) { // 至少出现2次
      return this.parseWatermarkText(bestText, Math.min(maxCount / 5, 1))
    }

    return { success: false, error: `未找到尺寸${patternSize}的有效图案` }
  }

  /**
   * 方法2: 文本识别提取
   */
  private extractByTextRecognition(imageData: ImageData): ExtractionResult {
    // 创建增强对比度的图像
    const enhancedData = this.enhanceContrast(imageData)
    
    // 寻找文本区域
    const textRegions = this.findTextRegions(enhancedData)
    
    for (const region of textRegions) {
      const text = this.extractTextFromRegion(enhancedData, region)
      if (text && this.isWatermarkText(text)) {
        return this.parseWatermarkText(text, 0.8)
      }
    }
    
    return { success: false, error: '未找到文本信息' }
  }

  /**
   * 方法3: 对比度增强提取
   */
  private extractByContrastEnhancement(imageData: ImageData): ExtractionResult {
    const enhanced = this.enhanceContrast(imageData, 3.0) // 大幅增强对比度
    
    // 转换为灰度并二值化
    const binary = this.toBinary(enhanced)
    
    // 寻找文本模式
    const text = this.recognizeTextFromBinary(binary)
    
    if (text && this.isWatermarkText(text)) {
      return this.parseWatermarkText(text, 0.7)
    }
    
    return { success: false, error: '对比度增强后未找到信息' }
  }

  /**
   * 方法4: 频域分析提取
   */
  private extractByFrequencyAnalysis(imageData: ImageData): ExtractionResult {
    // 简化的频域分析
    const lowFreqData = this.extractLowFrequency(imageData)
    const text = this.analyzeFrequencyPattern(lowFreqData)
    
    if (text && this.isWatermarkText(text)) {
      return this.parseWatermarkText(text, 0.6)
    }
    
    return { success: false, error: '频域分析未找到信息' }
  }

  /**
   * 提取增强图案
   */
  private extractEnhancedPattern(data: Uint8ClampedArray, x: number, y: number, size: number, width: number): {
    features: number[]
    noisePoints: Array<{x: number, y: number, intensity: number}>
    gridLines: Array<{type: 'h' | 'v', position: number}>
  } {
    const features: number[] = []
    const noisePoints: Array<{x: number, y: number, intensity: number}> = []
    const gridLines: Array<{type: 'h' | 'v', position: number}> = []

    // 提取噪点特征
    for (let py = 0; py < size && y + py < data.length / width / 4; py += 2) {
      for (let px = 0; px < size && x + px < width; px += 2) {
        const index = ((y + py) * width + (x + px)) * 4
        const gray = (data[index] + data[index + 1] + data[index + 2]) / 3

        // 检测暗点（可能的噪点）
        if (gray < 100) {
          noisePoints.push({ x: px, y: py, intensity: 255 - gray })
        }

        features.push(Math.floor(gray / 32)) // 量化为8级
      }
    }

    // 检测网格线
    this.detectGridLines(data, x, y, size, width, gridLines)

    return { features, noisePoints, gridLines }
  }

  /**
   * 检测网格线
   */
  private detectGridLines(data: Uint8ClampedArray, x: number, y: number, size: number, width: number, gridLines: Array<{type: 'h' | 'v', position: number}>): void {
    const threshold = 120
    const minLineLength = size * 0.6

    // 检测水平线
    for (let py = 0; py < size; py += 5) {
      let lineLength = 0
      for (let px = 0; px < size; px++) {
        const index = ((y + py) * width + (x + px)) * 4
        const gray = (data[index] + data[index + 1] + data[index + 2]) / 3

        if (gray < threshold) {
          lineLength++
        } else {
          if (lineLength > minLineLength) {
            gridLines.push({ type: 'h', position: py })
            break
          }
          lineLength = 0
        }
      }
    }

    // 检测垂直线
    for (let px = 0; px < size; px += 5) {
      let lineLength = 0
      for (let py = 0; py < size; py++) {
        const index = ((y + py) * width + (x + px)) * 4
        const gray = (data[index] + data[index + 1] + data[index + 2]) / 3

        if (gray < threshold) {
          lineLength++
        } else {
          if (lineLength > minLineLength) {
            gridLines.push({ type: 'v', position: px })
            break
          }
          lineLength = 0
        }
      }
    }
  }

  /**
   * 从图案中提取文本
   */
  private extractTextFromPattern(data: Uint8ClampedArray, x: number, y: number, size: number, width: number): string {
    // 寻找文本区域（通常在图案的上部）
    const textRegion = {
      x: x + 10,
      y: y + 20,
      width: size - 20,
      height: Math.floor(size / 2)
    }

    return this.extractTextFromRegion(data, textRegion, width)
  }

  /**
   * 图案转换为键值
   */
  private patternToKey(pattern: number[]): string {
    // 简化图案为特征值
    const features: number[] = []
    const step = Math.floor(pattern.length / 16) // 取16个特征点
    
    for (let i = 0; i < pattern.length; i += step) {
      features.push(pattern[i] || 0)
    }
    
    return features.join(',')
  }

  /**
   * 解码图案
   */
  private decodePattern(patternKey: string): string {
    // 这里应该实现图案到文本的解码
    // 简化实现：尝试从图案中提取可能的文本信息
    return patternKey.split(',').map(n => String.fromCharCode(parseInt(n) % 128)).join('')
  }

  /**
   * 增强对比度
   */
  private enhanceContrast(imageData: ImageData, factor: number = 2.0): ImageData {
    const enhanced = new ImageData(imageData.width, imageData.height)
    const data = imageData.data
    const newData = enhanced.data
    
    for (let i = 0; i < data.length; i += 4) {
      // 增强对比度
      newData[i] = Math.min(255, Math.max(0, (data[i] - 128) * factor + 128))     // R
      newData[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * factor + 128)) // G
      newData[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * factor + 128)) // B
      newData[i + 3] = data[i + 3] // A
    }
    
    return enhanced
  }

  /**
   * 寻找文本区域
   */
  private findTextRegions(imageData: ImageData): Array<{x: number, y: number, width: number, height: number}> {
    // 简化实现：返回一些可能的文本区域
    const regions = []
    const step = 50
    
    for (let y = 0; y < imageData.height - 100; y += step) {
      for (let x = 0; x < imageData.width - 200; x += step) {
        regions.push({ x, y, width: 200, height: 100 })
      }
    }
    
    return regions
  }

  /**
   * 从区域提取文本
   */
  private extractTextFromRegion(data: Uint8ClampedArray | ImageData, region: {x: number, y: number, width: number, height: number}, width?: number): string {
    let imageData: Uint8ClampedArray
    let imageWidth: number

    if (data instanceof ImageData) {
      imageData = data.data
      imageWidth = data.width
    } else {
      imageData = data
      imageWidth = width!
    }

    // 创建文本区域的二值化图像
    const binaryText = this.createBinaryTextImage(imageData, region, imageWidth)

    // 尝试识别字符
    const recognizedText = this.recognizeCharacters(binaryText, region.width, region.height)

    return recognizedText
  }

  /**
   * 创建二值化文本图像
   */
  private createBinaryTextImage(data: Uint8ClampedArray, region: {x: number, y: number, width: number, height: number}, imageWidth: number): number[] {
    const binary: number[] = []
    const threshold = 128

    for (let y = 0; y < region.height; y++) {
      for (let x = 0; x < region.width; x++) {
        const srcX = region.x + x
        const srcY = region.y + y

        if (srcX >= 0 && srcX < imageWidth && srcY >= 0) {
          const index = (srcY * imageWidth + srcX) * 4
          const gray = (data[index] + data[index + 1] + data[index + 2]) / 3
          binary.push(gray < threshold ? 1 : 0)
        } else {
          binary.push(0)
        }
      }
    }

    return binary
  }

  /**
   * 识别字符
   */
  private recognizeCharacters(binaryData: number[], width: number, height: number): string {
    // 简化的字符识别 - 寻找JSON模式
    const lines = this.extractTextLines(binaryData, width, height)

    // 合并所有行
    const fullText = lines.join('')

    // 寻找JSON模式
    const jsonPattern = /\{"u":"[^"]*","t":\d+,"p":"[^"]*"[^}]*\}/g
    const jsonMatch = fullText.match(jsonPattern)

    if (jsonMatch) {
      return jsonMatch[0]
    }

    // 寻找简化标识
    const wmPattern = /WM:[A-Za-z0-9\u4e00-\u9fa5]+/g
    const wmMatch = fullText.match(wmPattern)

    if (wmMatch) {
      return wmMatch[0]
    }

    return fullText
  }

  /**
   * 提取文本行
   */
  private extractTextLines(binaryData: number[], width: number, height: number): string[] {
    const lines: string[] = []
    const minLineHeight = 8

    for (let y = 0; y < height - minLineHeight; y += minLineHeight) {
      const line = this.extractSingleLine(binaryData, width, y, minLineHeight)
      if (line.trim()) {
        lines.push(line)
      }
    }

    return lines
  }

  /**
   * 提取单行文本
   */
  private extractSingleLine(binaryData: number[], width: number, startY: number, lineHeight: number): string {
    let line = ''

    // 简化的字符识别 - 基于连通区域
    for (let x = 0; x < width - 6; x += 6) { // 假设字符宽度约6像素
      const charPattern = this.extractCharPattern(binaryData, x, startY, 6, lineHeight, width)
      const char = this.patternToChar(charPattern)
      if (char) {
        line += char
      }
    }

    return line
  }

  /**
   * 提取字符图案
   */
  private extractCharPattern(binaryData: number[], x: number, y: number, charWidth: number, charHeight: number, imageWidth: number): number[] {
    const pattern: number[] = []

    for (let cy = 0; cy < charHeight; cy++) {
      for (let cx = 0; cx < charWidth; cx++) {
        const index = (y + cy) * imageWidth + (x + cx)
        pattern.push(binaryData[index] || 0)
      }
    }

    return pattern
  }

  /**
   * 图案转字符
   */
  private patternToChar(pattern: number[]): string {
    // 简化的字符识别 - 基于图案密度和形状
    const density = pattern.reduce((sum, val) => sum + val, 0) / pattern.length

    if (density < 0.1) return '' // 空白
    if (density > 0.8) return '#' // 实心

    // 基于密度范围猜测字符
    if (density > 0.6) return '8'
    if (density > 0.5) return '0'
    if (density > 0.4) return 'o'
    if (density > 0.3) return ':'
    if (density > 0.2) return '.'
    if (density > 0.1) return ','

    return ''
  }

  /**
   * 检查是否为水印文本
   */
  private isWatermarkText(text: string): boolean {
    // 检查文本是否包含水印特征
    return text.includes('{"u":') || text.includes('"t":') || text.includes('WM:')
  }

  /**
   * 解析水印文本
   */
  private parseWatermarkText(text: string, confidence: number): ExtractionResult {
    try {
      // 尝试解析JSON格式的水印信息
      const jsonMatch = text.match(/\{"u":[^}]+\}/g)
      if (jsonMatch) {
        const watermarkData = JSON.parse(jsonMatch[0])
        return {
          success: true,
          data: {
            user: watermarkData.u,
            timestamp: watermarkData.t,
            path: watermarkData.p,
            params: watermarkData.r,
            extractedAt: Date.now(),
            confidence
          },
          rawText: text
        }
      }
      
      // 尝试解析简化格式
      const wmMatch = text.match(/WM:([^,\s]+)/g)
      if (wmMatch) {
        const user = wmMatch[0].replace('WM:', '')
        return {
          success: true,
          data: {
            user,
            timestamp: Date.now(),
            path: '未知',
            params: null,
            extractedAt: Date.now(),
            confidence: confidence * 0.5
          },
          rawText: text
        }
      }
      
      return { success: false, error: '无法解析水印格式' }
    } catch (error) {
      return { success: false, error: `解析失败: ${error instanceof Error ? error.message : '未知错误'}` }
    }
  }

  /**
   * 转换为二值图像
   */
  private toBinary(imageData: ImageData): ImageData {
    const binary = new ImageData(imageData.width, imageData.height)
    const threshold = 128
    
    for (let i = 0; i < imageData.data.length; i += 4) {
      const gray = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3
      const value = gray > threshold ? 255 : 0
      
      binary.data[i] = value
      binary.data[i + 1] = value
      binary.data[i + 2] = value
      binary.data[i + 3] = 255
    }
    
    return binary
  }

  /**
   * 从二值图像识别文本
   */
  private recognizeTextFromBinary(imageData: ImageData): string {
    // 简化的文本识别
    // 实际应用中需要更复杂的OCR算法
    return ''
  }

  /**
   * 提取低频信息
   */
  private extractLowFrequency(imageData: ImageData): ImageData {
    // 简化的低频提取
    return imageData
  }

  /**
   * 分析频域图案
   */
  private analyzeFrequencyPattern(imageData: ImageData): string {
    // 简化的频域分析
    return ''
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    this.canvas.width = 0
    this.canvas.height = 0
  }
}

/**
 * 便捷函数：从文件提取水印
 */
export async function extractWatermarkFromFile(file: File): Promise<ExtractionResult> {
  const extractor = new WatermarkExtractor()
  try {
    return await extractor.extractFromFile(file)
  } finally {
    extractor.destroy()
  }
}

/**
 * 便捷函数：从URL提取水印
 */
export async function extractWatermarkFromUrl(url: string): Promise<ExtractionResult> {
  const extractor = new WatermarkExtractor()
  try {
    return await extractor.extractFromUrl(url)
  } finally {
    extractor.destroy()
  }
}
