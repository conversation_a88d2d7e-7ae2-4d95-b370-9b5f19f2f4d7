/**
 * 暗水印工具类
 * 基于频域的LSB+DCT混合水印技术
 * 具有强抗压缩和抗干扰能力
 */

import { Matrix } from 'ml-matrix'

export interface WatermarkConfig {
  /** 水印强度 (0.001-0.1) */
  strength: number
  /** 块大小 (8x8 或 16x16) */
  blockSize: number
  /** 水印透明度 */
  alpha: number
  /** 是否启用抗压缩优化 */
  compressionResistant: boolean
}

export interface WatermarkData {
  /** 用户信息 */
  user: string
  /** 时间戳 */
  timestamp: number
  /** 页面路径 */
  path: string
  /** 路由参数 */
  params: Record<string, any>
}

export class AdvancedWatermark {
  private config: WatermarkConfig
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D

  constructor(config: Partial<WatermarkConfig> = {}) {
    this.config = {
      strength: 0.02,
      blockSize: 8,
      alpha: 0.005,
      compressionResistant: true,
      ...config
    }
    
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }

  /**
   * 生成暗水印图像
   */
  async generateWatermark(data: WatermarkData): Promise<string> {
    // 1. 创建水印内容
    const watermarkText = this.encodeWatermarkData(data)
    
    // 2. 生成基础图案
    const basePattern = await this.createBasePattern(watermarkText)
    
    // 3. 应用频域变换
    const frequencyDomainPattern = this.applyFrequencyTransform(basePattern)
    
    // 4. 生成平铺水印
    return this.createTiledWatermark(frequencyDomainPattern)
  }

  /**
   * 编码水印数据为字符串
   */
  private encodeWatermarkData(data: WatermarkData): string {
    const encoded = {
      u: data.user,
      t: data.timestamp,
      p: data.path,
      r: data.params
    }
    return JSON.stringify(encoded)
  }

  /**
   * 创建基础图案
   */
  private async createBasePattern(text: string): Promise<ImageData> {
    const size = 64 // 基础图案大小
    this.canvas.width = size
    this.canvas.height = size
    
    // 清空画布
    this.ctx.fillStyle = 'transparent'
    this.ctx.fillRect(0, 0, size, size)
    
    // 使用文本哈希生成伪随机图案
    const hash = this.simpleHash(text)
    const pattern = this.generatePatternFromHash(hash, size)
    
    // 绘制图案
    const imageData = this.ctx.createImageData(size, size)
    for (let i = 0; i < pattern.length; i++) {
      const pixelIndex = i * 4
      const value = pattern[i] * 255
      imageData.data[pixelIndex] = value     // R
      imageData.data[pixelIndex + 1] = value // G
      imageData.data[pixelIndex + 2] = value // B
      imageData.data[pixelIndex + 3] = Math.floor(255 * this.config.alpha) // A
    }
    
    return imageData
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 从哈希值生成图案
   */
  private generatePatternFromHash(hash: number, size: number): number[] {
    const pattern: number[] = []
    let seed = hash
    
    for (let i = 0; i < size * size; i++) {
      // 线性同余生成器
      seed = (seed * 1103515245 + 12345) & 0x7fffffff
      const normalized = (seed / 0x7fffffff)
      
      // 生成低频噪声图案
      const x = i % size
      const y = Math.floor(i / size)
      const centerX = size / 2
      const centerY = size / 2
      const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2)
      const maxDistance = Math.sqrt(centerX ** 2 + centerY ** 2)
      
      // 低频权重（中心区域权重更高）
      const lowFreqWeight = 1 - (distance / maxDistance)
      const value = normalized * lowFreqWeight * this.config.strength
      
      pattern.push(value)
    }
    
    return pattern
  }

  /**
   * 应用频域变换（简化的DCT）
   */
  private applyFrequencyTransform(imageData: ImageData): ImageData {
    if (!this.config.compressionResistant) {
      return imageData
    }

    const { width, height } = imageData
    const blockSize = this.config.blockSize
    const result = new ImageData(width, height)
    
    // 复制原始数据
    for (let i = 0; i < imageData.data.length; i++) {
      result.data[i] = imageData.data[i]
    }

    // 对每个8x8或16x16块应用DCT变换
    for (let y = 0; y < height; y += blockSize) {
      for (let x = 0; x < width; x += blockSize) {
        this.applyDCTToBlock(result, x, y, blockSize)
      }
    }

    return result
  }

  /**
   * 对单个块应用DCT变换
   */
  private applyDCTToBlock(imageData: ImageData, startX: number, startY: number, blockSize: number) {
    const { width } = imageData
    const block: number[] = []
    
    // 提取块数据（只处理灰度值）
    for (let y = 0; y < blockSize && startY + y < imageData.height; y++) {
      for (let x = 0; x < blockSize && startX + x < width; x++) {
        const pixelIndex = ((startY + y) * width + (startX + x)) * 4
        const gray = imageData.data[pixelIndex] // 使用红色通道作为灰度值
        block.push(gray)
      }
    }

    // 简化的DCT变换（只处理低频系数）
    const transformed = this.simpleDCT(block, blockSize)
    
    // 将变换后的数据写回
    let blockIndex = 0
    for (let y = 0; y < blockSize && startY + y < imageData.height; y++) {
      for (let x = 0; x < blockSize && startX + x < width; x++) {
        const pixelIndex = ((startY + y) * width + (startX + x)) * 4
        const value = Math.max(0, Math.min(255, transformed[blockIndex]))
        
        imageData.data[pixelIndex] = value     // R
        imageData.data[pixelIndex + 1] = value // G
        imageData.data[pixelIndex + 2] = value // B
        // Alpha保持不变
        
        blockIndex++
      }
    }
  }

  /**
   * 简化的DCT变换
   */
  private simpleDCT(block: number[], blockSize: number): number[] {
    const result = [...block]
    
    // 只增强低频分量（左上角区域）
    const lowFreqSize = Math.floor(blockSize / 2)
    for (let v = 0; v < lowFreqSize; v++) {
      for (let u = 0; u < lowFreqSize; u++) {
        const index = v * blockSize + u
        if (index < result.length) {
          // 增强低频系数以提高抗压缩能力
          result[index] *= (1 + this.config.strength)
        }
      }
    }
    
    return result
  }

  /**
   * 创建平铺水印
   */
  private createTiledWatermark(pattern: ImageData): string {
    const screenW = window.innerWidth
    const screenH = window.innerHeight
    const patternSize = pattern.width
    
    // 计算需要的平铺数量
    const tilesX = Math.ceil(screenW * 2.5 / patternSize) + 1
    const tilesY = Math.ceil(screenH * 2.5 / patternSize) + 1
    
    const totalW = tilesX * patternSize
    const totalH = tilesY * patternSize
    
    // 创建大画布
    this.canvas.width = totalW
    this.canvas.height = totalH
    this.ctx.clearRect(0, 0, totalW, totalH)
    
    // 平铺图案（棋盘格分布）
    for (let row = 0; row < tilesY; row++) {
      for (let col = 0; col < tilesX; col++) {
        // 棋盘格模式：只在特定位置绘制
        if ((row + col) % 2 === 1) {
          const x = col * patternSize
          const y = row * patternSize
          this.ctx.putImageData(pattern, x, y)
        }
      }
    }
    
    return this.canvas.toDataURL('image/png')
  }

  /**
   * 提取水印信息（用于验证）
   */
  async extractWatermark(imageUrl: string): Promise<WatermarkData | null> {
    try {
      // 加载图像
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      return new Promise((resolve) => {
        img.onload = () => {
          this.canvas.width = img.width
          this.canvas.height = img.height
          this.ctx.drawImage(img, 0, 0)
          
          const imageData = this.ctx.getImageData(0, 0, img.width, img.height)
          const extracted = this.extractFromImageData(imageData)
          resolve(extracted)
        }
        img.onerror = () => resolve(null)
        img.src = imageUrl
      })
    } catch (error) {
      console.error('水印提取失败:', error)
      return null
    }
  }

  /**
   * 从图像数据中提取水印
   */
  private extractFromImageData(imageData: ImageData): WatermarkData | null {
    // 这里实现水印提取逻辑
    // 由于是演示版本，返回null
    // 实际应用中需要实现完整的逆变换过程
    return null
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<WatermarkConfig>) {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 销毁资源
   */
  destroy() {
    // 清理资源
    if (this.canvas) {
      this.canvas.width = 0
      this.canvas.height = 0
    }
  }
}

/**
 * 创建默认水印实例
 */
export function createWatermark(config?: Partial<WatermarkConfig>): AdvancedWatermark {
  return new AdvancedWatermark(config)
}

/**
 * 快速生成水印的便捷函数
 */
export async function generateQuickWatermark(data: WatermarkData): Promise<string> {
  const watermark = createWatermark()
  try {
    return await watermark.generateWatermark(data)
  } finally {
    watermark.destroy()
  }
}
