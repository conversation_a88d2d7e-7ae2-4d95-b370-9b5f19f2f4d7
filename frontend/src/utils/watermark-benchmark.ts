/**
 * 水印提取性能基准测试工具
 */

import { AdvancedWatermarkExtractor, type AdvancedExtractionResult } from './advanced-watermark-extractor'

export interface BenchmarkResult {
  testName: string
  description: string
  imageSize: { width: number; height: number }
  processingTime: number
  memoryUsage?: number
  extractionResult: AdvancedExtractionResult
  performance: {
    accuracy: number
    speed: number
    reliability: number
    overall: number
  }
}

export interface BenchmarkConfig {
  iterations: number
  imageFormats: string[]
  imageSizes: Array<{ width: number; height: number }>
  compressionLevels: number[]
  noiseTypes: string[]
  enableMemoryProfiling: boolean
}

export class WatermarkBenchmark {
  private extractor: AdvancedWatermarkExtractor
  private config: BenchmarkConfig

  constructor(config: Partial<BenchmarkConfig> = {}) {
    this.extractor = new AdvancedWatermarkExtractor()
    this.config = {
      iterations: 5,
      imageFormats: ['png', 'jpeg', 'webp'],
      imageSizes: [
        { width: 512, height: 512 },
        { width: 1024, height: 1024 },
        { width: 2048, height: 2048 }
      ],
      compressionLevels: [50, 70, 90],
      noiseTypes: ['gaussian', 'salt-pepper', 'uniform'],
      enableMemoryProfiling: false,
      ...config
    }
  }

  /**
   * 运行完整基准测试
   */
  async runFullBenchmark(testImages: HTMLImageElement[]): Promise<BenchmarkResult[]> {
    const results: BenchmarkResult[] = []
    
    console.log('开始水印提取基准测试...')
    
    for (const image of testImages) {
      // 基础提取测试
      const baselineResult = await this.runBaselineTest(image)
      results.push(baselineResult)
      
      // 压缩测试
      for (const quality of this.config.compressionLevels) {
        const compressionResult = await this.runCompressionTest(image, quality)
        results.push(compressionResult)
      }
      
      // 尺寸测试
      for (const size of this.config.imageSizes) {
        const sizeResult = await this.runSizeTest(image, size)
        results.push(sizeResult)
      }
      
      // 噪声测试
      for (const noiseType of this.config.noiseTypes) {
        const noiseResult = await this.runNoiseTest(image, noiseType)
        results.push(noiseResult)
      }
    }
    
    console.log('基准测试完成，共', results.length, '个测试用例')
    return results
  }

  /**
   * 基线测试 - 原始图像提取
   */
  private async runBaselineTest(image: HTMLImageElement): Promise<BenchmarkResult> {
    const testName = 'baseline'
    const description = '原始图像水印提取基线测试'
    
    const { result, processingTime, memoryUsage } = await this.measureExtraction(image)
    
    return {
      testName,
      description,
      imageSize: { width: image.width, height: image.height },
      processingTime,
      memoryUsage,
      extractionResult: result,
      performance: this.calculatePerformanceMetrics(result, processingTime)
    }
  }

  /**
   * 压缩测试
   */
  private async runCompressionTest(image: HTMLImageElement, quality: number): Promise<BenchmarkResult> {
    const testName = `compression-${quality}`
    const description = `JPEG压缩质量${quality}%测试`
    
    const compressedImage = await this.compressImage(image, quality)
    const { result, processingTime, memoryUsage } = await this.measureExtraction(compressedImage)
    
    return {
      testName,
      description,
      imageSize: { width: compressedImage.width, height: compressedImage.height },
      processingTime,
      memoryUsage,
      extractionResult: result,
      performance: this.calculatePerformanceMetrics(result, processingTime)
    }
  }

  /**
   * 尺寸测试
   */
  private async runSizeTest(image: HTMLImageElement, targetSize: { width: number; height: number }): Promise<BenchmarkResult> {
    const testName = `size-${targetSize.width}x${targetSize.height}`
    const description = `图像尺寸${targetSize.width}x${targetSize.height}测试`
    
    const resizedImage = await this.resizeImage(image, targetSize)
    const { result, processingTime, memoryUsage } = await this.measureExtraction(resizedImage)
    
    return {
      testName,
      description,
      imageSize: targetSize,
      processingTime,
      memoryUsage,
      extractionResult: result,
      performance: this.calculatePerformanceMetrics(result, processingTime)
    }
  }

  /**
   * 噪声测试
   */
  private async runNoiseTest(image: HTMLImageElement, noiseType: string): Promise<BenchmarkResult> {
    const testName = `noise-${noiseType}`
    const description = `${noiseType}噪声干扰测试`
    
    const noisyImage = await this.addNoise(image, noiseType)
    const { result, processingTime, memoryUsage } = await this.measureExtraction(noisyImage)
    
    return {
      testName,
      description,
      imageSize: { width: noisyImage.width, height: noisyImage.height },
      processingTime,
      memoryUsage,
      extractionResult: result,
      performance: this.calculatePerformanceMetrics(result, processingTime)
    }
  }

  /**
   * 测量提取性能
   */
  private async measureExtraction(image: HTMLImageElement): Promise<{
    result: AdvancedExtractionResult
    processingTime: number
    memoryUsage?: number
  }> {
    const startTime = performance.now()
    let startMemory: number | undefined
    
    if (this.config.enableMemoryProfiling && 'memory' in performance) {
      startMemory = (performance as any).memory.usedJSHeapSize
    }
    
    // 运行多次迭代取平均值
    const results: AdvancedExtractionResult[] = []
    const times: number[] = []
    
    for (let i = 0; i < this.config.iterations; i++) {
      const iterationStart = performance.now()
      const result = await this.extractor.extractFromImage(image)
      const iterationTime = performance.now() - iterationStart
      
      results.push(result)
      times.push(iterationTime)
    }
    
    // 选择最佳结果
    const bestResult = results.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    )
    
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length
    const totalTime = performance.now() - startTime
    
    let memoryUsage: number | undefined
    if (startMemory && 'memory' in performance) {
      const endMemory = (performance as any).memory.usedJSHeapSize
      memoryUsage = endMemory - startMemory
    }
    
    return {
      result: bestResult,
      processingTime: avgTime,
      memoryUsage
    }
  }

  /**
   * 计算性能指标
   */
  private calculatePerformanceMetrics(result: AdvancedExtractionResult, processingTime: number): {
    accuracy: number
    speed: number
    reliability: number
    overall: number
  } {
    // 准确性评分 (基于置信度)
    const accuracy = result.success ? result.confidence * 100 : 0
    
    // 速度评分 (基于处理时间，越快越好)
    const speed = Math.max(0, 100 - (processingTime / 100)) // 假设100ms为基准
    
    // 可靠性评分 (基于是否成功提取)
    const reliability = result.success ? 100 : 0
    
    // 综合评分
    const overall = (accuracy * 0.4 + speed * 0.3 + reliability * 0.3)
    
    return {
      accuracy: Math.round(accuracy),
      speed: Math.round(speed),
      reliability: Math.round(reliability),
      overall: Math.round(overall)
    }
  }

  /**
   * 压缩图像
   */
  private async compressImage(image: HTMLImageElement, quality: number): Promise<HTMLImageElement> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = image.width
      canvas.height = image.height
      ctx.drawImage(image, 0, 0)
      
      const dataUrl = canvas.toDataURL('image/jpeg', quality / 100)
      
      const newImage = new Image()
      newImage.onload = () => resolve(newImage)
      newImage.src = dataUrl
    })
  }

  /**
   * 调整图像尺寸
   */
  private async resizeImage(image: HTMLImageElement, targetSize: { width: number; height: number }): Promise<HTMLImageElement> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = targetSize.width
      canvas.height = targetSize.height
      ctx.drawImage(image, 0, 0, targetSize.width, targetSize.height)
      
      const dataUrl = canvas.toDataURL('image/png')
      
      const newImage = new Image()
      newImage.onload = () => resolve(newImage)
      newImage.src = dataUrl
    })
  }

  /**
   * 添加噪声
   */
  private async addNoise(image: HTMLImageElement, noiseType: string): Promise<HTMLImageElement> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = image.width
      canvas.height = image.height
      ctx.drawImage(image, 0, 0)
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      
      switch (noiseType) {
        case 'gaussian':
          this.addGaussianNoise(data)
          break
        case 'salt-pepper':
          this.addSaltPepperNoise(data)
          break
        case 'uniform':
          this.addUniformNoise(data)
          break
      }
      
      ctx.putImageData(imageData, 0, 0)
      
      const dataUrl = canvas.toDataURL('image/png')
      
      const newImage = new Image()
      newImage.onload = () => resolve(newImage)
      newImage.src = dataUrl
    })
  }

  /**
   * 添加高斯噪声
   */
  private addGaussianNoise(data: Uint8ClampedArray): void {
    for (let i = 0; i < data.length; i += 4) {
      const noise = this.gaussianRandom() * 20
      data[i] = Math.max(0, Math.min(255, data[i] + noise))
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise))
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise))
    }
  }

  /**
   * 添加椒盐噪声
   */
  private addSaltPepperNoise(data: Uint8ClampedArray): void {
    for (let i = 0; i < data.length; i += 4) {
      if (Math.random() < 0.05) { // 5% 概率
        const value = Math.random() < 0.5 ? 0 : 255
        data[i] = value
        data[i + 1] = value
        data[i + 2] = value
      }
    }
  }

  /**
   * 添加均匀噪声
   */
  private addUniformNoise(data: Uint8ClampedArray): void {
    for (let i = 0; i < data.length; i += 4) {
      const noise = (Math.random() - 0.5) * 40
      data[i] = Math.max(0, Math.min(255, data[i] + noise))
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise))
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise))
    }
  }

  /**
   * 生成高斯随机数
   */
  private gaussianRandom(): number {
    let u = 0, v = 0
    while (u === 0) u = Math.random()
    while (v === 0) v = Math.random()
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v)
  }

  /**
   * 生成基准测试报告
   */
  generateReport(results: BenchmarkResult[]): string {
    const report = {
      summary: {
        totalTests: results.length,
        successfulExtractions: results.filter(r => r.extractionResult.success).length,
        averageProcessingTime: results.reduce((sum, r) => sum + r.processingTime, 0) / results.length,
        averageAccuracy: results.reduce((sum, r) => sum + r.performance.accuracy, 0) / results.length,
        overallScore: results.reduce((sum, r) => sum + r.performance.overall, 0) / results.length
      },
      detailedResults: results,
      recommendations: this.generateRecommendations(results)
    }
    
    return JSON.stringify(report, null, 2)
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(results: BenchmarkResult[]): string[] {
    const recommendations: string[] = []
    
    const avgAccuracy = results.reduce((sum, r) => sum + r.performance.accuracy, 0) / results.length
    if (avgAccuracy < 60) {
      recommendations.push('整体准确率较低，建议优化水印嵌入算法')
    }
    
    const avgSpeed = results.reduce((sum, r) => sum + r.performance.speed, 0) / results.length
    if (avgSpeed < 50) {
      recommendations.push('处理速度较慢，建议优化提取算法性能')
    }
    
    const compressionTests = results.filter(r => r.testName.includes('compression'))
    const compressionSuccess = compressionTests.filter(r => r.extractionResult.success).length
    if (compressionSuccess / compressionTests.length < 0.7) {
      recommendations.push('压缩抗性较弱，建议使用频域水印技术')
    }
    
    return recommendations
  }
}
