/**
 * 水印组合器
 * 支持多种水印技术的组合使用
 */

import { AdvancedWatermark, WatermarkData, WatermarkConfig } from './watermark'

export interface ComposerConfig {
  /** 启用的水印技术 */
  techniques: WatermarkTechnique[]
  /** 全局透明度 */
  globalAlpha: number
  /** 是否启用动态刷新 */
  dynamicRefresh: boolean
  /** 刷新间隔（毫秒） */
  refreshInterval: number
}

export type WatermarkTechnique = 'frequency' | 'css' | 'svg' | 'webgl'

export interface WatermarkLayer {
  id: string
  technique: WatermarkTechnique
  element: HTMLElement
  config: any
}

export class WatermarkComposer {
  private config: ComposerConfig
  private layers: Map<string, WatermarkLayer> = new Map()
  private refreshTimer?: number
  private isDestroyed = false

  constructor(config: Partial<ComposerConfig> = {}) {
    this.config = {
      techniques: ['frequency', 'css'],
      globalAlpha: 0.001,
      dynamicRefresh: true,
      refreshInterval: 60000, // 1分钟
      ...config
    }
  }

  /**
   * 初始化水印系统
   */
  async initialize(container: HTMLElement, data: WatermarkData): Promise<void> {
    if (this.isDestroyed) return

    // 清理现有图层
    this.clearLayers()

    // 根据配置创建不同技术的水印图层
    for (const technique of this.config.techniques) {
      try {
        await this.createLayer(technique, container, data)
      } catch (error) {
        console.warn(`水印技术 ${technique} 初始化失败:`, error)
      }
    }

    // 启动动态刷新
    if (this.config.dynamicRefresh) {
      this.startDynamicRefresh(data)
    }
  }

  /**
   * 创建水印图层
   */
  private async createLayer(
    technique: WatermarkTechnique,
    container: HTMLElement,
    data: WatermarkData
  ): Promise<void> {
    const layerId = `watermark-${technique}-${Date.now()}`

    switch (technique) {
      case 'frequency':
        await this.createFrequencyLayer(layerId, container, data)
        break
      case 'css':
        await this.createCSSLayer(layerId, container, data)
        break
      case 'svg':
        await this.createSVGLayer(layerId, container, data)
        break
      case 'webgl':
        await this.createWebGLLayer(layerId, container, data)
        break
    }
  }

  /**
   * 创建频域水印图层
   */
  private async createFrequencyLayer(
    layerId: string,
    container: HTMLElement,
    data: WatermarkData
  ): Promise<void> {
    const watermark = new AdvancedWatermark({
      strength: this.config.globalAlpha * 10, // 基于全局透明度调整
      alpha: this.config.globalAlpha,
      compressionResistant: true
    })

    const watermarkUrl = await watermark.generateWatermark(data)

    const element = document.createElement('div')
    element.id = layerId
    element.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9999;
      background-image: url(${watermarkUrl});
      background-repeat: repeat;
      opacity: ${this.config.globalAlpha * 200};
      mix-blend-mode: multiply;
    `

    container.appendChild(element)

    this.layers.set(layerId, {
      id: layerId,
      technique: 'frequency',
      element,
      config: { watermark }
    })
  }

  /**
   * 创建CSS滤镜水印图层
   */
  private async createCSSLayer(
    layerId: string,
    container: HTMLElement,
    data: WatermarkData
  ): Promise<void> {
    const element = document.createElement('div')
    element.id = layerId
    
    // 使用CSS生成伪随机图案
    const hash = this.generateHash(JSON.stringify(data))
    const pattern = this.generateCSSPattern(hash)
    
    element.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9998;
      opacity: ${this.config.globalAlpha * 100};
      background: ${pattern};
      mix-blend-mode: overlay;
    `

    container.appendChild(element)

    this.layers.set(layerId, {
      id: layerId,
      technique: 'css',
      element,
      config: { hash }
    })
  }

  /**
   * 创建SVG水印图层
   */
  private async createSVGLayer(
    layerId: string,
    container: HTMLElement,
    data: WatermarkData
  ): Promise<void> {
    const svgContent = this.generateSVGWatermark(data)
    const svgUrl = `data:image/svg+xml;utf8,${encodeURIComponent(svgContent)}`

    const element = document.createElement('div')
    element.id = layerId
    element.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9997;
      background-image: url(${svgUrl});
      background-repeat: repeat;
      opacity: ${this.config.globalAlpha * 150};
      mix-blend-mode: soft-light;
    `

    container.appendChild(element)

    this.layers.set(layerId, {
      id: layerId,
      technique: 'svg',
      element,
      config: { svgContent }
    })
  }

  /**
   * 创建WebGL水印图层（实验性）
   */
  private async createWebGLLayer(
    layerId: string,
    container: HTMLElement,
    data: WatermarkData
  ): Promise<void> {
    // WebGL实现较复杂，这里提供基础框架
    const canvas = document.createElement('canvas')
    canvas.id = layerId
    canvas.style.cssText = `
      position: fixed;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      z-index: 9996;
      opacity: ${this.config.globalAlpha * 100};
    `

    // 基础WebGL设置
    const gl = canvas.getContext('webgl')
    if (gl) {
      this.initWebGLWatermark(gl, data)
    }

    container.appendChild(canvas)

    this.layers.set(layerId, {
      id: layerId,
      technique: 'webgl',
      element: canvas,
      config: { gl, data }
    })
  }

  /**
   * 生成哈希值
   */
  private generateHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(16)
  }

  /**
   * 生成CSS图案
   */
  private generateCSSPattern(hash: string): string {
    const colors = []
    for (let i = 0; i < 6; i += 2) {
      const r = parseInt(hash.substr(i, 2) || '00', 16)
      const alpha = this.config.globalAlpha * 0.1
      colors.push(`rgba(${r}, ${r}, ${r}, ${alpha})`)
    }

    return `
      radial-gradient(circle at 25% 25%, ${colors[0]} 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, ${colors[1]} 0%, transparent 50%),
      radial-gradient(circle at 25% 75%, ${colors[2]} 0%, transparent 50%)
    `
  }

  /**
   * 生成SVG水印
   */
  private generateSVGWatermark(data: WatermarkData): string {
    const size = 100
    const hash = this.generateHash(JSON.stringify(data))
    
    // 基于哈希生成图案
    const patterns = []
    for (let i = 0; i < hash.length && i < 8; i += 2) {
      const x = (parseInt(hash[i], 16) / 15) * size
      const y = (parseInt(hash[i + 1] || '0', 16) / 15) * size
      const r = 2 + (parseInt(hash[i], 16) / 15) * 3
      
      patterns.push(`<circle cx="${x}" cy="${y}" r="${r}" fill="rgba(0,0,0,${this.config.globalAlpha})" />`)
    }

    return `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        ${patterns.join('')}
      </svg>
    `
  }

  /**
   * 初始化WebGL水印
   */
  private initWebGLWatermark(gl: WebGLRenderingContext, data: WatermarkData): void {
    // 基础WebGL着色器
    const vertexShaderSource = `
      attribute vec2 a_position;
      varying vec2 v_texCoord;
      void main() {
        gl_Position = vec4(a_position, 0.0, 1.0);
        v_texCoord = a_position * 0.5 + 0.5;
      }
    `

    const fragmentShaderSource = `
      precision mediump float;
      varying vec2 v_texCoord;
      uniform float u_time;
      uniform vec2 u_resolution;
      
      void main() {
        vec2 uv = v_texCoord;
        float pattern = sin(uv.x * 50.0) * sin(uv.y * 50.0) * 0.1;
        gl_FragColor = vec4(pattern, pattern, pattern, ${this.config.globalAlpha});
      }
    `

    // 编译着色器和创建程序的代码...
    // 这里简化实现
  }

  /**
   * 启动动态刷新
   */
  private startDynamicRefresh(data: WatermarkData): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }

    this.refreshTimer = window.setInterval(() => {
      if (this.isDestroyed) return

      // 更新时间戳
      const updatedData = {
        ...data,
        timestamp: Date.now()
      }

      // 重新生成水印
      this.refreshWatermarks(updatedData)
    }, this.config.refreshInterval)
  }

  /**
   * 刷新水印
   */
  private async refreshWatermarks(data: WatermarkData): Promise<void> {
    this.layers.forEach(async (layer, layerId) => {
      try {
        await this.updateLayer(layer, data)
      } catch (error) {
        console.warn(`刷新水印图层 ${layerId} 失败:`, error)
      }
    })
  }

  /**
   * 更新图层
   */
  private async updateLayer(layer: WatermarkLayer, data: WatermarkData): Promise<void> {
    switch (layer.technique) {
      case 'frequency':
        const watermark = layer.config.watermark as AdvancedWatermark
        const newUrl = await watermark.generateWatermark(data)
        layer.element.style.backgroundImage = `url(${newUrl})`
        break
      
      case 'css':
        const hash = this.generateHash(JSON.stringify(data))
        const pattern = this.generateCSSPattern(hash)
        layer.element.style.background = pattern
        break
      
      case 'svg':
        const svgContent = this.generateSVGWatermark(data)
        const svgUrl = `data:image/svg+xml;utf8,${encodeURIComponent(svgContent)}`
        layer.element.style.backgroundImage = `url(${svgUrl})`
        break
    }
  }

  /**
   * 清理所有图层
   */
  private clearLayers(): void {
    this.layers.forEach((layer, layerId) => {
      if (layer.element.parentNode) {
        layer.element.parentNode.removeChild(layer.element)
      }

      // 清理特定资源
      if (layer.technique === 'frequency' && layer.config.watermark) {
        layer.config.watermark.destroy()
      }
    })

    this.layers.clear()
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ComposerConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 更新水印数据
   */
  async updateData(data: WatermarkData): Promise<void> {
    if (this.isDestroyed) return

    // 更新所有图层的数据
    for (const [layerId, layer] of this.layers) {
      try {
        switch (layer.technique) {
          case 'frequency':
            await this.updateFrequencyLayer(layer, data)
            break
          case 'css':
            await this.updateCSSLayer(layer, data)
            break
          case 'svg':
            await this.updateSVGLayer(layer, data)
            break
          case 'webgl':
            await this.updateWebGLLayer(layer, data)
            break
        }
      } catch (error) {
        console.warn(`更新水印图层 ${layerId} 失败:`, error)
      }
    }
  }

  /**
   * 更新频域水印图层
   */
  private async updateFrequencyLayer(layer: WatermarkLayer, data: WatermarkData): Promise<void> {
    if (layer.config.watermark) {
      const watermarkUrl = await layer.config.watermark.generateWatermark(data)
      layer.element.style.backgroundImage = `url(${watermarkUrl})`
    }
  }

  /**
   * 更新CSS水印图层
   */
  private async updateCSSLayer(layer: WatermarkLayer, data: WatermarkData): Promise<void> {
    const hash = this.generateDataHash(data)
    const pattern = this.generateCSSPattern(hash)
    layer.element.style.backgroundImage = pattern
  }

  /**
   * 更新SVG水印图层
   */
  private async updateSVGLayer(layer: WatermarkLayer, data: WatermarkData): Promise<void> {
    const hash = this.generateDataHash(data)
    const svgPattern = this.generateSVGPattern(hash)
    layer.element.style.backgroundImage = `url("data:image/svg+xml,${encodeURIComponent(svgPattern)}")`
  }

  /**
   * 更新WebGL水印图层
   */
  private async updateWebGLLayer(layer: WatermarkLayer, data: WatermarkData): Promise<void> {
    // WebGL图层更新逻辑
    console.log('WebGL图层更新暂未实现')
  }

  /**
   * 销毁水印系统
   */
  destroy(): void {
    this.isDestroyed = true

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = undefined
    }

    this.clearLayers()
  }
}

/**
 * 创建水印组合器实例
 */
export function createWatermarkComposer(config?: Partial<ComposerConfig>): WatermarkComposer {
  return new WatermarkComposer(config)
}
