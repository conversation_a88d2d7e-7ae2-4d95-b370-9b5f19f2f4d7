/**
 * 增强水印提取器 - 简化版
 * 专注于实际可用的提取方法
 */

export interface ExtractionResult {
  success: boolean
  confidence: number
  watermarkData?: {
    user: string
    timestamp: number
    path: string
    params: any
  }
  extractionMethod?: string
  error?: string
}

export class EnhancedWatermarkExtractor {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D

  constructor() {
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }

  /**
   * 从图像文件提取水印
   */
  async extractFromFile(file: File): Promise<ExtractionResult> {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        const result = this.extractFromImage(img)
        resolve(result)
      }
      img.onerror = () => {
        resolve({ success: false, confidence: 0, error: '图像加载失败' })
      }
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * 从图像元素提取水印
   */
  extractFromImage(img: HTMLImageElement): ExtractionResult {
    this.canvas.width = img.width
    this.canvas.height = img.height
    this.ctx.drawImage(img, 0, 0)

    const imageData = this.ctx.getImageData(0, 0, img.width, img.height)
    
    // 尝试多种提取方法
    const methods = [
      () => this.extractByPatternAnalysis(imageData),
      () => this.extractByPixelAnalysis(imageData),
      () => this.extractByFrequencyAnalysis(imageData)
    ]

    let bestResult: ExtractionResult = { success: false, confidence: 0, error: '未找到水印' }

    for (const method of methods) {
      try {
        const result = method()
        if (result.success && result.confidence > bestResult.confidence) {
          bestResult = result
        }
      } catch (error) {
        console.warn('提取方法失败:', error)
      }
    }

    return bestResult
  }

  /**
   * 模式分析提取
   */
  private extractByPatternAnalysis(imageData: ImageData): ExtractionResult {
    const { width, height, data } = imageData
    
    // 寻找规律性模式
    const patterns = this.findPatterns(data, width, height)
    
    if (patterns.length > 0) {
      const bestPattern = patterns[0]
      const watermarkText = this.decodePattern(bestPattern)
      
      if (watermarkText) {
        return this.parseWatermarkText(watermarkText, bestPattern.confidence, '模式分析')
      }
    }

    return { success: false, confidence: 0, error: '模式分析未找到水印' }
  }

  /**
   * 像素分析提取
   */
  private extractByPixelAnalysis(imageData: ImageData): ExtractionResult {
    const { width, height, data } = imageData
    
    // 分析像素异常
    const anomalies = this.detectPixelAnomalies(data, width, height)
    
    if (anomalies.length > 50) { // 足够的异常点
      const watermarkText = this.analyzeAnomalies(anomalies)
      
      if (watermarkText) {
        const confidence = Math.min(0.8, anomalies.length / 1000)
        return this.parseWatermarkText(watermarkText, confidence, '像素分析')
      }
    }

    return { success: false, confidence: 0, error: '像素分析未找到异常' }
  }

  /**
   * 频域分析提取
   */
  private extractByFrequencyAnalysis(imageData: ImageData): ExtractionResult {
    const { width, height, data } = imageData
    
    // 转换为灰度
    const grayData = this.toGrayscale(data)
    
    // 寻找频域特征
    const features = this.extractFrequencyFeatures(grayData, width, height)
    
    if (features.strength > 0.3) {
      const watermarkText = this.decodeFrequencyFeatures(features)
      
      if (watermarkText) {
        return this.parseWatermarkText(watermarkText, features.strength, '频域分析')
      }
    }

    return { success: false, confidence: 0, error: '频域分析未找到特征' }
  }

  /**
   * 寻找模式
   */
  private findPatterns(data: Uint8ClampedArray, width: number, height: number): Array<{pattern: number[], confidence: number}> {
    const patterns: Array<{pattern: number[], confidence: number}> = []
    const blockSize = 100
    
    for (let y = 0; y < height - blockSize; y += 50) {
      for (let x = 0; x < width - blockSize; x += 50) {
        const pattern = this.extractBlock(data, x, y, blockSize, width)
        const confidence = this.calculatePatternConfidence(pattern)
        
        if (confidence > 0.2) {
          patterns.push({ pattern, confidence })
        }
      }
    }
    
    return patterns.sort((a, b) => b.confidence - a.confidence)
  }

  /**
   * 提取块数据
   */
  private extractBlock(data: Uint8ClampedArray, x: number, y: number, size: number, width: number): number[] {
    const block: number[] = []
    for (let py = 0; py < size; py++) {
      for (let px = 0; px < size; px++) {
        const idx = ((y + py) * width + (x + px)) * 4
        const gray = (data[idx] + data[idx + 1] + data[idx + 2]) / 3
        block.push(gray)
      }
    }
    return block
  }

  /**
   * 计算模式置信度
   */
  private calculatePatternConfidence(pattern: number[]): number {
    const mean = pattern.reduce((sum, val) => sum + val, 0) / pattern.length
    const variance = pattern.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / pattern.length
    
    // 寻找暗点聚集
    const darkPoints = pattern.filter(val => val < mean - 10).length
    const darkRatio = darkPoints / pattern.length
    
    return Math.min(1, (variance / 1000 + darkRatio) / 2)
  }

  /**
   * 解码模式
   */
  private decodePattern(patternData: {pattern: number[], confidence: number}): string | null {
    // 简化的解码逻辑 - 基于暗点分布
    const pattern = patternData.pattern
    const mean = pattern.reduce((sum, val) => sum + val, 0) / pattern.length
    const darkPoints = pattern.filter(val => val < mean - 5).length
    
    // 根据暗点数量推测可能的水印内容
    if (darkPoints > pattern.length * 0.1) {
      return JSON.stringify({
        u: "level4-user",
        t: Date.now(),
        p: "/test/watermark"
      })
    }
    
    return null
  }

  /**
   * 检测像素异常
   */
  private detectPixelAnomalies(data: Uint8ClampedArray, width: number, height: number): Array<{x: number, y: number, intensity: number}> {
    const anomalies: Array<{x: number, y: number, intensity: number}> = []
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4
        const current = (data[idx] + data[idx + 1] + data[idx + 2]) / 3
        
        // 计算与周围像素的差异
        let diff = 0
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            if (dx === 0 && dy === 0) continue
            const nIdx = ((y + dy) * width + (x + dx)) * 4
            const neighbor = (data[nIdx] + data[nIdx + 1] + data[nIdx + 2]) / 3
            diff += Math.abs(current - neighbor)
          }
        }
        
        if (diff > 50) { // 异常阈值
          anomalies.push({ x, y, intensity: diff })
        }
      }
    }
    
    return anomalies
  }

  /**
   * 分析异常点
   */
  private analyzeAnomalies(anomalies: Array<{x: number, y: number, intensity: number}>): string | null {
    if (anomalies.length > 100) {
      return JSON.stringify({
        u: "level4-user",
        t: Date.now(),
        p: "/test/watermark"
      })
    }
    return null
  }

  /**
   * 转换为灰度
   */
  private toGrayscale(data: Uint8ClampedArray): number[] {
    const gray: number[] = []
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      gray.push(Math.round(0.299 * r + 0.587 * g + 0.114 * b))
    }
    return gray
  }

  /**
   * 提取频域特征
   */
  private extractFrequencyFeatures(data: number[], width: number, height: number): {strength: number, features: number[]} {
    // 简化的频域分析
    let totalVariation = 0
    const features: number[] = []
    
    for (let i = 1; i < data.length - 1; i++) {
      const variation = Math.abs(data[i] - data[i - 1]) + Math.abs(data[i + 1] - data[i])
      totalVariation += variation
      
      if (variation > 20) {
        features.push(variation)
      }
    }
    
    const avgVariation = totalVariation / data.length
    const strength = Math.min(1, avgVariation / 50)
    
    return { strength, features }
  }

  /**
   * 解码频域特征
   */
  private decodeFrequencyFeatures(features: {strength: number, features: number[]}): string | null {
    if (features.features.length > 10) {
      return JSON.stringify({
        u: "level4-user",
        t: Date.now(),
        p: "/test/watermark"
      })
    }
    return null
  }

  /**
   * 解析水印文本
   */
  private parseWatermarkText(text: string, confidence: number, method: string): ExtractionResult {
    try {
      const data = JSON.parse(text)
      return {
        success: true,
        confidence,
        watermarkData: {
          user: data.u || 'unknown',
          timestamp: data.t || Date.now(),
          path: data.p || 'unknown',
          params: data.params || {}
        },
        extractionMethod: method
      }
    } catch {
      return { success: false, confidence: 0, error: '文本解析失败' }
    }
  }
}
