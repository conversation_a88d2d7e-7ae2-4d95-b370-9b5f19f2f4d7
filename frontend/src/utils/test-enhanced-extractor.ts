/**
 * 增强提取器测试工具
 */

import { EnhancedWatermarkExtractor } from './enhanced-watermark-extractor'

export async function testEnhancedExtractor() {
  const extractor = new EnhancedWatermarkExtractor()
  
  // 创建一个测试图像
  const canvas = document.createElement('canvas')
  canvas.width = 500
  canvas.height = 500
  const ctx = canvas.getContext('2d')!
  
  // 绘制基础背景
  ctx.fillStyle = '#f0f0f0'
  ctx.fillRect(0, 0, 500, 500)
  
  // 添加一些模拟的水印模式
  ctx.fillStyle = 'rgba(0, 0, 0, 0.01)'
  for (let i = 0; i < 100; i++) {
    const x = Math.random() * 400 + 50
    const y = Math.random() * 400 + 50
    ctx.fillRect(x, y, 2, 2)
  }
  
  // 转换为图像
  return new Promise<void>((resolve) => {
    canvas.toBlob(async (blob) => {
      if (!blob) {
        console.error('无法创建测试图像')
        resolve()
        return
      }
      
      const file = new File([blob], 'test.png', { type: 'image/png' })
      
      try {
        const result = await extractor.extractFromFile(file)
        console.log('增强提取器测试结果:', result)
        
        if (result.success) {
          console.log('✅ 增强提取器工作正常')
        } else {
          console.log('ℹ️ 增强提取器未检测到水印（正常，因为是模拟数据）')
        }
      } catch (error) {
        console.error('❌ 增强提取器测试失败:', error)
      }
      
      resolve()
    })
  })
}

// 自动测试（仅在开发环境）
if (import.meta.env.MODE === 'development') {
  // 延迟执行，避免影响页面加载
  setTimeout(() => {
    testEnhancedExtractor()
  }, 2000)
}
