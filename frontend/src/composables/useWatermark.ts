/**
 * 水印组合式函数
 * 提供Vue3响应式的水印管理功能
 */

import { ref, onMounted, onUnmounted, watch, computed, readonly } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { WatermarkComposer, createWatermarkComposer, ComposerConfig, WatermarkTechnique } from '@/utils/watermark-composer'
import { WatermarkData } from '@/utils/watermark'

export interface UseWatermarkOptions {
  /** 启用的水印技术 */
  techniques?: WatermarkTechnique[]
  /** 水印强度 */
  strength?: number
  /** 是否自动启动 */
  autoStart?: boolean
  /** 是否启用动态刷新 */
  dynamicRefresh?: boolean
  /** 刷新间隔（毫秒） */
  refreshInterval?: number
  /** 容器选择器 */
  container?: string | HTMLElement
}

export function useWatermark(options: UseWatermarkOptions = {}) {
  const route = useRoute()
  const userStore = useUserStore()
  
  // 响应式状态
  const isEnabled = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const composer = ref<WatermarkComposer | null>(null)
  
  // 配置
  const config = computed<ComposerConfig>(() => ({
    techniques: options.techniques || ['frequency', 'css'],
    globalAlpha: options.strength || 0.005,
    dynamicRefresh: options.dynamicRefresh ?? true,
    refreshInterval: options.refreshInterval || 60000
  }))
  
  // 水印数据
  const watermarkData = computed<WatermarkData>(() => ({
    user: userStore.name || '未知用户',
    timestamp: Date.now(),
    path: route.fullPath,
    params: route.params
  }))
  
  // 容器元素
  const containerElement = computed(() => {
    if (typeof options.container === 'string') {
      return document.querySelector(options.container) as HTMLElement
    }
    return options.container || document.body
  })

  /**
   * 启动水印
   */
  const start = async (): Promise<void> => {
    if (isEnabled.value || isLoading.value) return
    
    try {
      isLoading.value = true
      error.value = null
      
      // 创建水印组合器
      composer.value = createWatermarkComposer(config.value)
      
      // 初始化水印
      const container = containerElement.value
      if (!container) {
        throw new Error('未找到水印容器元素')
      }
      
      await composer.value.initialize(container, watermarkData.value)
      isEnabled.value = true
      
      console.log('水印系统启动成功')
    } catch (err) {
      error.value = err instanceof Error ? err.message : '水印启动失败'
      console.error('水印启动失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 停止水印
   */
  const stop = (): void => {
    if (!isEnabled.value) return
    
    try {
      if (composer.value) {
        composer.value.destroy()
        composer.value = null
      }
      
      isEnabled.value = false
      error.value = null
      
      console.log('水印系统已停止')
    } catch (err) {
      console.error('水印停止失败:', err)
    }
  }

  /**
   * 重启水印
   */
  const restart = async (): Promise<void> => {
    stop()
    await start()
  }

  /**
   * 更新配置
   */
  const updateConfig = (newOptions: Partial<UseWatermarkOptions>): void => {
    Object.assign(options, newOptions)
    
    if (composer.value) {
      composer.value.updateConfig(config.value)
    }
  }

  /**
   * 手动刷新水印
   */
  const refresh = async (): Promise<void> => {
    if (!isEnabled.value || !composer.value) return
    
    try {
      isLoading.value = true
      const container = containerElement.value
      if (container) {
        await composer.value.initialize(container, watermarkData.value)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '水印刷新失败'
      console.error('水印刷新失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 检查浏览器兼容性
   */
  const checkCompatibility = (): { 
    supported: boolean
    features: Record<string, boolean>
    recommendations: WatermarkTechnique[]
  } => {
    const features = {
      canvas: !!document.createElement('canvas').getContext,
      webgl: !!document.createElement('canvas').getContext('webgl'),
      css3: CSS.supports('mix-blend-mode', 'multiply'),
      svg: !!document.createElementNS,
      imageData: !!CanvasRenderingContext2D.prototype.createImageData
    }
    
    const recommendations: WatermarkTechnique[] = []
    
    if (features.canvas && features.imageData) {
      recommendations.push('frequency')
    }
    
    if (features.css3) {
      recommendations.push('css')
    }
    
    if (features.svg) {
      recommendations.push('svg')
    }
    
    if (features.webgl) {
      recommendations.push('webgl')
    }
    
    return {
      supported: recommendations.length > 0,
      features,
      recommendations
    }
  }

  /**
   * 获取性能统计
   */
  const getPerformanceStats = () => {
    return {
      isEnabled: isEnabled.value,
      isLoading: isLoading.value,
      hasError: !!error.value,
      layerCount: composer.value ? 'unknown' : 0, // 实际实现中可以从composer获取
      memoryUsage: (performance as any).memory ? {
        used: Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round((performance as any).memory.totalJSHeapSize / 1024 / 1024)
      } : null
    }
  }

  // 监听路由变化
  watch(
    () => route.fullPath,
    () => {
      if (isEnabled.value && config.value.dynamicRefresh) {
        refresh()
      }
    }
  )

  // 监听用户变化
  watch(
    () => userStore.name,
    () => {
      if (isEnabled.value) {
        refresh()
      }
    }
  )

  // 生命周期管理
  onMounted(() => {
    if (options.autoStart !== false) {
      start()
    }
  })

  onUnmounted(() => {
    stop()
  })

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏时暂停刷新
      if (composer.value) {
        composer.value.updateConfig({ dynamicRefresh: false })
      }
    } else {
      // 页面显示时恢复刷新
      if (composer.value && config.value.dynamicRefresh) {
        composer.value.updateConfig({ dynamicRefresh: true })
        refresh()
      }
    }
  }

  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange)
  })

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })

  return {
    // 状态
    isEnabled: readonly(isEnabled),
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // 计算属性
    watermarkData: readonly(watermarkData),
    config: readonly(config),
    
    // 方法
    start,
    stop,
    restart,
    refresh,
    updateConfig,
    
    // 工具方法
    checkCompatibility,
    getPerformanceStats
  }
}

/**
 * 全局水印管理器
 */
class GlobalWatermarkManager {
  private instances = new Set<ReturnType<typeof useWatermark>>()
  
  register(instance: ReturnType<typeof useWatermark>) {
    this.instances.add(instance)
  }
  
  unregister(instance: ReturnType<typeof useWatermark>) {
    this.instances.delete(instance)
  }
  
  async startAll() {
    const promises = Array.from(this.instances).map(instance => instance.start())
    await Promise.allSettled(promises)
  }
  
  stopAll() {
    this.instances.forEach(instance => instance.stop())
  }
  
  async refreshAll() {
    const promises = Array.from(this.instances).map(instance => instance.refresh())
    await Promise.allSettled(promises)
  }
  
  getStats() {
    return Array.from(this.instances).map(instance => instance.getPerformanceStats())
  }
}

export const globalWatermarkManager = new GlobalWatermarkManager()

/**
 * 带全局管理的水印Hook
 */
export function useGlobalWatermark(options: UseWatermarkOptions = {}) {
  const watermark = useWatermark(options)
  
  onMounted(() => {
    globalWatermarkManager.register(watermark)
  })
  
  onUnmounted(() => {
    globalWatermarkManager.unregister(watermark)
  })
  
  return watermark
}
