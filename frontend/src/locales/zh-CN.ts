export default {
    route: {
        dashboard: '仪表盘',
        system: '系统管理',
        user: '用户管理',
        role: '角色管理'
    },
    login: {
        title: '系统登录',
        username: '用户名',
        password: '密码',
        login: '登录',
        captcha: '验证码',
        rememberMe: '记住我',
        forgotPassword: '忘记密码？',
        register: '注册新用户',
    },
    forgotPassword: {
        title: '忘记密码',
        username: '用户名',
        verifyMethod: '验证方式',
        email: '邮箱',
        phone: '手机',
        captcha: '验证码',
        next: '下一步',
        back: '返回登录'
    },
    resetPassword: {
        title: '重置密码',
        verificationCode: '验证码',
        newPassword: '新密码',
        confirmPassword: '确认密码',
        submit: '提交',
        back: '返回',
        success: '密码重置成功'
    },
    navbar: {
        dashboard: '首页',
        logout: '退出登录',
        profile: '个人信息',
        theme: '换肤',
        size: '布局大小'
    },
    system: {
        user: {
            title: '用户管理',
            username: '用户名',
            nickname: '昵称',
            email: '邮箱',
            mobile: '手机号',
            role: '角色',
            status: '状态',
            createTime: '创建时间',
            operation: '操作',
            search: '搜索',
            add: '新增',
            edit: '编辑',
            delete: '删除',
            enable: '启用',
            disable: '禁用',
            confirmDelete: '确定要删除该用户吗？',
            form: {
                username: '请输入用户名',
                password: '请输入密码',
                nickname: '请输入昵称',
                email: '请输入邮箱',
                mobile: '请输入手机号',
                role: '请选择角色'
            }
        },
        role: {
            title: '角色管理',
            name: '角色名称',
            code: '角色编码',
            status: '状态',
            createTime: '创建时间',
            operation: '操作',
            search: '搜索',
            add: '新增',
            edit: '编辑',
            delete: '删除',
            permission: '权限设置',
            enable: '启用',
            disable: '禁用',
            confirmDelete: '确定要删除该角色吗？',
            form: {
                name: '请输入角色名称',
                code: '请输入角色编码'
            }
        },
        position: {
            title: '岗位管理',
            code: '岗位编码',
            name: '岗位名称',
            role: '关联角色',
            status: '状态',
            description: '描述',
            operation: '操作',
            search: '搜索',
            add: '新增岗位',
            edit: '编辑',
            delete: '删除',
            enable: '启用',
            disable: '禁用',
            confirmDelete: '确定要删除该岗位吗？',
            form: {
                code: '请输入岗位编码',
                name: '请输入岗位名称',
                role: '请选择关联角色',
                description: '请输入描述'
            }
        }
    },
    error: {
        pageNotFound: '页面不存在',
        backToHome: '返回首页',
        pageNoRight: '抱歉，您没有权限访问此页面。'
    },
    register: {
        title: '用户注册',
        gotoLogin: '已有账号？去登录',
        label: {
            username: '用户名',
            password: '用户密码',
            confirmPassword: '确认密码',
            email: '电子邮件',
            realname: '您的姓名',
            gender: '性别',
            country: '国家',
            unit: '注册单位名称',
            contactaddress: '联系地址',
            mobile: '手机号码',
            telephone: '固定电话',
            btnRegister: '立即注册'
        },
        placeholder: {
            username: '请输入用户名',
            password: '请输入用户密码',
            confirmPassword: '请输入确认密码',
            email: '请输入邮箱',
            realname: '请输入您的姓名',
            gender: '请选择性别',
            country: '请选择国家',
            unit: '请输入注册单位名称',
            contactaddress: '请输入联系地址',
            mobile: '请输入手机号码',
            telephone: '请输入固定电话',
        },
        validate: {
            usernameLength: '用户名长度在 6 到 20 个字符',
            passwordComplex: '密码需包含大小写字母、数字、特殊字符，长度6-20位',
            passwordLength: '用户密码长度在 6 到 20 个字符',
            confirmPasswordComplex: '确认密码需包含大小写字母、数字、特殊字符，长度6-20位',
            confirmPasswordNotMatch: '两次输入的密码不一致',
            emailFormat: '请输入正确的邮箱地址',
            registerSuccess: '新增/注册成功',
            registerFailed: '提交表单失败'
        }
    },
    profile: {
        title: '个人资料',
        username: '用户名',
        realname: '真实姓名',
        email: '电子邮箱',
        mobile: '手机号码',
        gender: '性别',
        country: '国家',
        unit: '注册单位名称',
        contactaddress: '联系地址',
        telephone: '固定电话',
        usertype: {
            label: '用户类型',
            usertype1: '内部用户',
            usertype2: '外部用户',
            usertype3: '系统用户',
        },
        status: '账号状态',
        title2: '安全设置',
        accountPassword: '账号密码',
        accountPasswordDesc: '定期修改密码可以提高账号安全性',
        accountPasswordBtn: '修改密码',
        emailbind: '邮箱绑定',
        emailNobindDesc: '未绑定邮箱',
        emailbindDesc: '已绑定邮箱',
        emailbindBtn: '验证邮箱',
        phonebind: '手机绑定',
        phoneNobindDesc: '未绑定手机',
        phonebindDesc: '已绑定手机',
        phonebindBtn: '验证手机',
        verificationcode: '验证码',
        changepassword: {
            title: '修改密码',
            oldPassword: '当前密码',
            newPassword: '新密码',
            confirmNewPassword: '确认新密码',
            oldPasswordrequired: '请输入当前密码',
            newPasswordrequired: '请输入新密码',
            confirmNewPasswordrequired: '请再次输入新密码',
            passwordMismatch: '两次输入的密码不一致',
            passwordlength: '密码长度不能小于6个字符',
        },
        placeholder: {
            username: '请输入用户名',
            password: '请输入用户密码',
            confirmPassword: '请输入确认密码',
            email: '请输入邮箱',
            realname: '请输入您的姓名',
            gender: '请选择性别',
            country: '请选择国家',
            unit: '请输入注册单位名称',
            contactaddress: '请输入联系地址',
            mobile: '请输入手机号码',
            telephone: '请输入固定电话',
        }
    },
    menu: {
        trialSearch: '项目检索',
        dashboard: '首页',
        projectCenter: '项目中心',
        projectUserAdd: '注册新项目',
        projectApproval: '项目审核',
        projectSystemAllList: '所有项目',
        projectSystemPendingJudgeList: '待判断项目',
        projectSystemPendingSendNumberList: '待发号项目',
        projectSystemApplyEditList: '再修改申请项目',
        projectSystemPendingReviewList: '再修改复核项目',
        projectSystemReturnEditList: '再修改退回项目',
        projectSystemApprovedList: '已发号项目',
        projectSystemNonTraditionalList: '非传统医学项目',
        projectSystemAllSubmittedList: '审核状态查询',
        projectSystemPendingAssignList: '待分配项目',
        projectSystemPendingReviewList2: '待核审项目',
        projectSystemReviewReturnedList2: '已退回项目',
        projectSystemPendingApprovedList2: '已核审通过项目',
        projectSystemApprovedList2: '已发号项目',
        projectSystemReAssignList: '重新分配项目',
        projectSystemPendingAssignReviewList: '待分配项目',
        projectSystemPendingReviewList3: '待复审项目',
        projectSystemPendingApprovedList3: '已复审通过项目',
        projectSystemReviewReturnedList3: '已退回项目',
        projectSystemApprovedList3: '已发号项目',
        projectSystemPendingReviewList4: '待初审项目',
        projectSystemPendingApprovedList4: '已初审通过项目',
        projectSystemReviewReturnedList4: '已退回项目',
        projectSystemApprovedList4: '已发号项目',
        system: '系统管理',
        systemUser: '用户管理',
        systemLogging: '日志管理',
        systemRole: '角色管理',
        systemOrganization: '组织管理',
        systemPosition: '岗位管理',
        systemPermission: '权限管理',
        files: '存储管理',
        filesStorage: '存储管理',
        filesFileType: '文件类型管理',
        formManagement: '表单管理',
        formList: '表单定义',
        profileIndex: '个人中心',
        changePassword: '修改密码',
        projectUserAllList: '我的项目',
        projectUserPendingSubmit: '待提交项目',
        projectUserPendingApproval: '待审核项目',
        projectUserApprovedList: '已通过项目',
        projectAllList: '全部项目',
        messaging: '消息系统',
        messagingProvider: '服务商管理',
        messagingAccount: '账户管理',
        messagingTemplate: '模板管理',
        messagingSendRecords: '发送记录',
        messagingSendTest: '发送测试',
    },
    Footer: {
        CopyrightTop: '国际传统医学临床试验注册平台 京ICP备********号-5',
        CopyrightMiddle: '系统要求：Chrome 88+、Edge 88+、Safari 14+、360极速浏览器等现代浏览器',
        CopyrightBottom: '建议使用1920×1080或更高分辨率以获得最佳体验'
    },
    cancel: '取消',
    confirm: '确定',
    success: '成功',
    failed: '失败',
    normal: '正常',
    disabled: '禁用',
    locking: '锁定',
    enable: '启用',
    btnSave: '保存修改',
    btnReset: '重置',
}
