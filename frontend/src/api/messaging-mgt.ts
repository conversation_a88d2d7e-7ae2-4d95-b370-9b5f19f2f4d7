/**
 * 消息系统管理API
 */
import request from '@/utils/request'
import {
    MessageSendDto,
    MessageSendQueryCriteria,
    MessageTemplateDto,
    MessageTemplateQueryCriteria,
    CreateTemplateRequestDto,
    UpdateTemplateRequestDto,
    MessageProviderDto,
    MessageProviderQueryCriteria,
    CreateProviderRequestDto,
    UpdateProviderRequestDto,
    MessageAccountDto,
    MessageAccountQueryCriteria,
    CreateAccountRequestDto,
    UpdateAccountRequestDto,
    MessageAccountProviderDto,
    MessageAccountProviderQueryCriteria,
    AccountProviderBalanceDto,
    AccountBalanceSummaryDto,
    CreateAccountProviderRequestDto,
    UpdateAccountProviderRequestDto,
    AccountProviderRechargeRequestDto, MessageAccountRechargeQueryCriteria, MessageAccountRechargeDto
} from '@/dtos/messaging-mgt.dto'

import {getApiBaseUrl} from '@/config/env'
import {PageResult} from "@/dtos";

const BASE_URL = getApiBaseUrl('messaging-mgt')

/**
 * 消息发送记录API
 */
export const messageSendApi = {
    /**
     * 分页查询消息发送记录
     */
    getPage(params: MessageSendQueryCriteria) {
        return request.get<PageResult<MessageSendDto>>(`${BASE_URL}/MessageSend/page`, {params})
    },

    /**
     * 查询消息发送记录列表
     */
    getList(params: MessageSendQueryCriteria) {
        return request.get<MessageSendDto[]>(`${BASE_URL}/MessageSend`, {params})
    },

    /**
     * 根据ID查询消息发送记录
     */
    getById(id: number) {
        return request.get<MessageSendDto>(`${BASE_URL}/MessageSend/${id}`)
    }
}

/**
 * 消息模板API
 */
export const messageTemplateApi = {
    /**
     * 分页查询消息模板
     */
    getPage(params: MessageTemplateQueryCriteria) {
        return request.get<PageResult<MessageTemplateDto>>(`${BASE_URL}/MessageTemplate/page`, {params})
    },

    /**
     * 查询消息模板列表
     */
    getList(params: MessageTemplateQueryCriteria) {
        return request.get<MessageTemplateDto[]>(`${BASE_URL}/MessageTemplate`, {params})
    },

    /**
     * 根据编码查询模板
     */
    getByCode(templateCode: string, appCode: string) {
        return request.get<MessageTemplateDto>(`${BASE_URL}/MessageTemplate/by-code`, {
            params: {templateCode, appCode}
        })
    },

    /**
     * 根据应用查询模板列表
     */
    getByApp(appCode: string) {
        return request.get<MessageTemplateDto[]>(`${BASE_URL}/MessageTemplate/by-app/${appCode}`)
    },

    /**
     * 创建模板
     */
    create(data: CreateTemplateRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageTemplate`, data)
    },

    /**
     * 更新模板
     */
    update(id: number, data: UpdateTemplateRequestDto) {
        return request.put<boolean>(`${BASE_URL}/MessageTemplate/${id}`, data)
    },

    /**
     * 删除模板
     */
    delete(id: number) {
        return request.delete<boolean>(`${BASE_URL}/MessageTemplate/${id}`)
    },

    /**
     * 启用/禁用模板
     */
    setEnabled(id: number, isEnabled: boolean) {
        return request.put<boolean>(`${BASE_URL}/MessageTemplate/${id}/enabled`, isEnabled)
    }
}

/**
 * 消息服务商API
 */
export const messageProviderApi = {
    /**
     * 分页查询消息服务商
     */
    getPage(params: MessageProviderQueryCriteria) {
        return request.get<PageResult<MessageProviderDto>>(`${BASE_URL}/MessageProvider/page`, {params})
    },

    /**
     * 查询消息服务商列表
     */
    getList(params: MessageProviderQueryCriteria) {
        return request.get<MessageProviderDto[]>(`${BASE_URL}/MessageProvider`, {params})
    },

    /**
     * 获取所有服务商
     */
    getAll() {
        return request.get<MessageProviderDto[]>(`${BASE_URL}/MessageProvider`)
    },

    /**
     * 根据编码查询服务商
     */
    getByCode(providerCode: string) {
        return request.get<MessageProviderDto>(`${BASE_URL}/MessageProvider/by-code/${providerCode}`)
    },

    /**
     * 根据类型查询服务商列表
     */
    getByType(messageType: string) {
        return request.get<MessageProviderDto[]>(`${BASE_URL}/MessageProvider/by-type/${messageType}`)
    },

    /**
     * 创建服务商
     */
    create(data: CreateProviderRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageProvider`, data)
    },

    /**
     * 更新服务商
     */
    update(id: number, data: UpdateProviderRequestDto) {
        return request.put<boolean>(`${BASE_URL}/MessageProvider/${id}`, data)
    },

    /**
     * 删除服务商
     */
    delete(id: number) {
        return request.delete<boolean>(`${BASE_URL}/MessageProvider/${id}`)
    },

    /**
     * 启用/禁用服务商
     */
    setEnabled(id: number, isEnabled: boolean) {
        return request.put<boolean>(`${BASE_URL}/MessageProvider/${id}/enabled`, isEnabled)
    }
}

/**
 * 消息账户API
 */
export const messageAccountApi = {
    /**
     * 分页查询消息账户
     */
    getPage(params: MessageAccountQueryCriteria) {
        return request.get<PageResult<MessageAccountDto>>(`${BASE_URL}/MessageAccount/page`, {params})
    },

    /**
     * 查询消息账户列表
     */
    getList(params: MessageAccountQueryCriteria) {
        return request.get<MessageAccountDto[]>(`${BASE_URL}/MessageAccount`, {params})
    },

    /**
     * 根据编码查询账户
     */
    getByCode(accountCode: string) {
        return request.get<MessageAccountDto>(`${BASE_URL}/MessageAccount/by-code/${accountCode}`)
    },

    /**
     * 根据应用查询账户列表
     */
    getByApp(appCode: string) {
        return request.get<MessageAccountDto[]>(`${BASE_URL}/MessageAccount/by-app/${appCode}`)
    },

    /**
     * 创建账户
     */
    create(data: CreateAccountRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageAccount`, data)
    },

    /**
     * 更新账户
     */
    update(id: number, data: UpdateAccountRequestDto) {
        return request.put<boolean>(`${BASE_URL}/MessageAccount/${id}`, data)
    },

    /**
     * 删除账户
     */
    delete(id: number) {
        return request.delete<boolean>(`${BASE_URL}/MessageAccount/${id}`)
    },

    /**
     * 启用/禁用账户
     */
    setEnabled(id: number, isEnabled: boolean) {
        return request.put<boolean>(`${BASE_URL}/MessageAccount/${id}/enabled`, isEnabled)
    },

    /**
     * 账户充值
     */
    recharge(data: AccountProviderRechargeRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageAccount/recharge`, data)
    },

    /**
     * 查询账户余额（按服务商分组）
     */
    getBalance(accountCode: string) {
        return request.get<AccountProviderBalanceDto[]>(`${BASE_URL}/MessageAccount/balance/${accountCode}`)
    },

    /**
     * 查询特定服务商的余额
     */
    getBalanceByProvider(accountCode: string, providerCode: string) {
        return request.get<AccountProviderBalanceDto>(`${BASE_URL}/MessageAccount/balance/${accountCode}/${providerCode}`)
    },

    /**
     * 查询账户余额汇总
     */
    getBalanceSummary(accountCode: string) {
        return request.get<AccountBalanceSummaryDto>(`${BASE_URL}/MessageAccount/balance-summary/${accountCode}`)
    }
}

/**
 * 账户-服务商关联API
 */
export const messageAccountProviderApi = {
    /**
     * 分页查询账户-服务商关联
     */
    getPage(params: MessageAccountProviderQueryCriteria) {
        return request.get<PageResult<MessageAccountProviderDto>>(`${BASE_URL}/MessageAccountProvider/page`, {params})
    },

    /**
     * 查询账户-服务商关联列表
     */
    getList(params: MessageAccountProviderQueryCriteria) {
        return request.get<MessageAccountProviderDto[]>(`${BASE_URL}/MessageAccountProvider`, {params})
    },

    /**
     * 根据账户查询关联配置
     */
    getByAccount(accountCode: string) {
        return request.get<MessageAccountProviderDto[]>(`${BASE_URL}/MessageAccountProvider/by-account/${accountCode}`)
    },

    /**
     * 根据服务商查询关联配置
     */
    getByProvider(providerCode: string) {
        return request.get<MessageAccountProviderDto[]>(`${BASE_URL}/MessageAccountProvider/by-provider/${providerCode}`)
    },

    /**
     * 查询特定关联配置
     */
    getByAccountAndProvider(accountCode: string, providerCode: string) {
        return request.get<MessageAccountProviderDto>(`${BASE_URL}/MessageAccountProvider/by-account-provider`, {
            params: {accountCode, providerCode}
        })
    },

    /**
     * 创建账户-服务商关联
     */
    create(data: CreateAccountProviderRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageAccountProvider`, data)
    },

    /**
     * 创建账户-服务商关联（别名）
     */
    createRelation(data: CreateAccountProviderRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageAccountProvider/create-relation`, data)
    },

    /**
     * 更新关联配置
     */
    update(id: number, data: UpdateAccountProviderRequestDto) {
        return request.put<boolean>(`${BASE_URL}/MessageAccountProvider/${id}`, data)
    },

    /**
     * 更新关联配置（别名）
     */
    updateConfig(id: number, data: UpdateAccountProviderRequestDto) {
        return request.put<boolean>(`${BASE_URL}/MessageAccountProvider/${id}/update-config`, data)
    },

    /**
     * 删除关联
     */
    delete(id: number) {
        return request.delete<boolean>(`${BASE_URL}/MessageAccountProvider/${id}`)
    },

    /**
     * 启用/禁用关联
     */
    setEnabled(id: number, isEnabled: boolean) {
        return request.put<boolean>(`${BASE_URL}/MessageAccountProvider/${id}/enabled/${isEnabled}`)
    },


    /**
     * 充值
     */
    recharge(data: AccountProviderRechargeRequestDto) {
        return request.post<boolean>(`${BASE_URL}/MessageAccountProvider/recharge`, data)
    },

    /**
     * 删除关联（别名）
     */
    removeRelation(accountCode: string, providerCode: string) {
        return request.delete<boolean>(`${BASE_URL}/MessageAccountProvider/remove-relation`, {
            params: {accountCode, providerCode}
        })
    },

    /**
     * 查询可用服务商
     */
    getAvailableProviders(accountCode: string, messageType: string) {
        return request.get<MessageAccountProviderDto[]>(`${BASE_URL}/MessageAccountProvider/available-providers`, {
            params: {accountCode, messageType}
        })
    }
}


export const messageAccountRechargeApi = {
    getPage(params: MessageAccountRechargeQueryCriteria) {
        return request.get<PageResult<MessageAccountRechargeDto>>(`${BASE_URL}/MessageAccountRecharge/page`, {params})
    }
}
