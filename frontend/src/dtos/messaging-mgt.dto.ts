/**
 * 消息系统管理相关DTO定义
 */


/**
 * 消息发送记录DTO
 */
export interface MessageSendDto {
    key: number
    appCode: string
    target: string
    templateCode: string
    messageType: string
    status: number
    content?: string
    variablesJson?: string
    providerCode?: string
    sendTime?: string
    errorInfo?: string
    tryTime: number
    createdTime: string
}

/**
 * 消息发送查询条件
 */
export interface MessageSendQueryCriteria {
    appCode?: string
    messageType?: string
    status?: number
    templateCode?: string
    target?: string
    startTime?: string
    endTime?: string
    dateRange?: [string, string]
    $pageIndex?: number
    $pageSize?: number
}

/**
 * 消息模板DTO
 */
export interface MessageTemplateDto {
    key: number
    templateCode: string
    templateName: string
    templateType: string
    appCode: string
    title: string
    content: string
    variables?: string
    isEnabled: boolean
    remark?: string
    createTime: string
}

/**
 * 消息模板查询条件
 */
export interface MessageTemplateQueryCriteria {
    templateCode?: string
    templateName?: string
    templateType?: string
    appCode?: string
    isEnabled?: boolean

    $pageIndex?: number
    $pageSize?: number
}

/**
 * 消息服务商DTO
 */
export interface MessageProviderDto {
    key: number
    providerCode: string
    providerName: string
    providerType: string
    providerInstace: string
    isEnabled: boolean
    config?: string
    remark?: string
    createTime: string
}

/**
 * 消息服务商查询条件
 */
export interface MessageProviderQueryCriteria {
    providerCode?: string
    providerName?: string
    providerType?: string
    isEnabled?: boolean

    $pageIndex?: number
    $pageSize?: number
}

/**
 * 账户DTO
 */
export interface MessageAccountDto {
    key: number
    accountCode: string
    accountName: string
    appCode: string
    isEnabled: boolean
    remark?: string
    createTime: string
}

/**
 * 账户查询条件
 */
export interface MessageAccountQueryCriteria {
    accountCode?: string
    accountName?: string
    appCode?: string
    isEnabled?: boolean

    $pageIndex?: number
    $pageSize?: number
}

/**
 * 账户-服务商关联DTO
 */
export interface MessageAccountProviderDto {
    key: number
    accountCode: string
    providerCode: string
    providerType: string
    priority: number
    quotaTotal?: number
    quotaUsed?: number
    isDefault?: boolean
    providerConfig?: string
    isEnabled: boolean
    remark?: string
    createTime: string
}

export interface MessageAccountRechargeDto {
    key: number
    accountCode: string
    providerCode: string
    amount: number
    balanceBefore: number
    balanceAfter: number
    rechargeTime: string
    operator: string
    remark?: string
    createTime: string
}

/**
 * 账户-服务商关联查询条件
 */
export interface MessageAccountProviderQueryCriteria {
    accountCode?: string
    providerCode?: string
    messageType?: string
    isEnabled?: boolean

    $pageIndex?: number
    $pageSize?: number
}

/**
 * 应用默认服务商DTO
 */
export interface MessageProviderDefaultDto {
    key: number
    appCode: string
    providerType: string
    providerCode: string
    priority: number
    isEnabled: boolean
    createTime: string
    $pageIndex?: number
    $pageSize?: number
}

export interface MessageAccountRechargeQueryCriteria {
    accountCode?: string
    providerCode?: string
    startTime?: string
    endTime?: string
    accountProviderId?: string
    $pageIndex?: number
    $pageSize?: number
}

/**
 * 账户-服务商余额DTO
 */
export interface AccountProviderBalanceDto {
    accountCode: string
    providerCode: string
    messageType: string
    totalQuota: number
    usedQuota: number
    remainingQuota: number
    priority: number
    isEnabled: boolean
}

/**
 * 账户余额汇总DTO
 */
export interface AccountBalanceSummaryDto {
    accountCode: string
    totalQuota: number
    usedQuota: number
    remainingQuota: number
    providerCount: number
    enabledProviderCount: number
}

/**
 * 设置默认服务商请求DTO
 */
export interface SetDefaultProviderRequestDto {
    appCode: string
    messageType: string
    providerCode: string
}

/**
 * 创建账户-服务商关联请求DTO
 */
export interface CreateAccountProviderRequestDto {
    accountCode: string
    providerCode: string
    providerType: string
    priority: number
    quotaTotal: number
    isDefault?: boolean
    providerConfig?: string
    remark?: string
    isEnabled: boolean
}

/**
 * 更新账户-服务商配置请求DTO
 */
export interface UpdateAccountProviderRequestDto {
    priority: number
    quotaTotal: number
    providerConfig?: string
    remark?: string
    isEnabled: boolean
}

/**
 * 账户-服务商充值请求DTO
 */
export interface AccountProviderRechargeRequestDto {
    accountCode: string
    providerCode: string
    amount: number
    remark?: string
}

/**
 * 发送消息请求DTO
 */
export interface SendMessageRequestDto {
    appCode: string
    target: string
    templateCode: string
    variablesJson?: string
    messageType: string
    providerCode?: string
}

/**
 * 创建模板请求DTO
 */
export interface CreateTemplateRequestDto {
    templateCode: string
    templateName: string
    appCode: string
    templateType: string
    title: string
    content: string
    variables?: string
    isEnabled: boolean
    remark?: string
}

/**
 * 更新模板请求DTO
 */
export interface UpdateTemplateRequestDto {
    templateName: string
    content: string
    variables?: string
    isEnabled: boolean
    remark?: string
}

/**
 * 创建服务商请求DTO
 */
export interface CreateProviderRequestDto {
    providerCode: string
    providerName: string
    providerType: string
    config: string
    isEnabled: boolean
    remark?: string
}

/**
 * 更新服务商请求DTO
 */
export interface UpdateProviderRequestDto {
    providerName: string
    config: string
    isEnabled: boolean
    remark?: string
}

/**
 * 创建账户请求DTO
 */
export interface CreateAccountRequestDto {
    accountCode: string
    accountName: string
    appCode: string
    isEnabled: boolean
    remark?: string
}

/**
 * 更新账户请求DTO
 */
export interface UpdateAccountRequestDto {
    accountName: string
    isEnabled: boolean
    remark?: string
}
