<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('profile.title') }}</span>
        </div>
      </template>
      <div class="profile-content" v-loading="loading">
        <div class="profile-avatar">
          <ImagePreview
              :file="userInfo.avatar"
              :size="100"
              fit="cover"
              :defaultUrl="base+'img/default-avatar.svg'"
              :cycle="true"
          />
          <div class="avatar-actions">
            <FileUploader
                language="zh"
                :type="'image'"
                :typeCode="'avatar'"
                :multiple="false"
                @update:files="onAvatarUpload"
            />
          </div>
        </div>
        <div class="profile-info">
          <el-form label-width="150px" :model="userInfo">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('profile.username')">
                  <el-input v-model="userInfo.username" disabled/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('profile.realname')">
                  <el-input v-model="userInfo.realName"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('profile.email')">
                  <el-input v-model="userInfo.email" disabled/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('profile.mobile')">
                  <el-input v-model="userInfo.mobile" disabled/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('profile.gender')" prop="gender">
                  <el-select v-model="userInfo.gender" :placeholder="$t('profile.placeholder.gender')" tabindex="6">
                    <el-option value="男" label="男"/>
                    <el-option value="女" label="女"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('profile.country')" prop="country">
                  <el-select v-model="userInfo.country" :placeholder="$t('profile.placeholder.country')" tabindex="7">
                    <el-option value="中国" label="中国"/>
                    <el-option value="其他" label="其他"/>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('profile.unit')" prop="unit">
                  <el-input v-model="userInfo.unit" :placeholder="$t('profile.placeholder.unit')" tabindex="8"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('profile.contactaddress')" prop="contactaddress">
                  <el-input
                      v-model="userInfo.contactAddress" :placeholder="$t('profile.placeholder.contactaddress')"
                      tabindex="9"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('profile.telephone')" prop="telephone">
                  <el-input v-model="userInfo.telephone" :placeholder="$t('profile.placeholder.telephone')"
                            tabindex="11"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('profile.usertype.label')">
                  <el-tag v-if="userInfo.userType === 1" type="success"
                  >{{ t('profile.usertype.usertype1') }}
                  </el-tag
                  >
                  <el-tag v-else-if="userInfo.userType === 2" type="info"
                  >{{ t('profile.usertype.usertype2') }}
                  </el-tag
                  >
                  <el-tag v-else-if="userInfo.userType === 3" type="warning"
                  >{{ t('profile.usertype.usertype3') }}
                  </el-tag
                  >
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('profile.status')">
                  <el-tag v-if="userInfo.status === 1" type="success">{{ t('normal') }}</el-tag>
                  <el-tag v-else-if="userInfo.status === 0" type="danger"
                  >{{ t('disabled') }}
                  </el-tag
                  >
                  <el-tag v-else-if="userInfo.status === 2" type="warning"
                  >{{ t('locking') }}
                  </el-tag
                  >
                </el-form-item>
              </el-col>

            </el-row>
            <el-row :gutter="20">
              <el-col :span="12" :offset="4">
                <el-button type="primary" @click="updateProfile"
                >{{ t('btnSave') }}
                </el-button
                >
                <el-button @click="resetForm">{{ t('btnReset') }}</el-button>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-card>

    <el-card class="security-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('profile.title2') }}</span>
        </div>
      </template>
      <div class="security-content">

        <el-row>
          <el-col :span="8">
            <div class="security-item">
              <div class="security-info">
                <h3>{{ t('profile.accountPassword') }}</h3>
                <p>{{ t('profile.accountPasswordDesc') }}</p>
              </div>
              <div class="security-action">
                <router-link to="/profile/change-password">
                  <el-button type="primary">{{ t('profile.accountPasswordBtn') }}</el-button>
                </router-link>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="security-item">
              <div class="security-info">
                <h3>{{ t('profile.emailbind') }}</h3>
                <p>
                  <template v-if="userInfo.email">
                    {{ t('profile.emailbindDesc') }}：{{ userInfo.email }}
                    <el-tag
                        v-if="userInfo.emailConfirmed"
                        type="success"
                        size="small"
                    >已验证
                    </el-tag
                    >
                    <el-tag v-else type="warning" size="small">未验证</el-tag>
                  </template>
                  <template v-else>{{ t('profile.emailNobindDesc') }}</template>
                </p>
              </div>
              <div class="security-action">
                <el-button
                    type="primary"
                    :disabled="!userInfo.email || userInfo.emailConfirmed"
                    @click="showEmailVerifyDialog"
                >
                  {{ t('profile.emailbindBtn') }}
                </el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="security-item">
              <div class="security-info">
                <h3>{{ t('profile.phonebind') }}</h3>
                <p>
                  <template v-if="userInfo.mobile">
                    {{ t('profile.phonebindDesc') }}：{{ userInfo.mobile }}
                    <el-tag
                        v-if="userInfo.mobileConfirmed"
                        type="success"
                        size="small"
                    >已验证
                    </el-tag
                    >
                    <el-tag v-else type="warning" size="small">未验证</el-tag>
                  </template>
                  <template v-else>{{ t('profile.phoneNobindDesc') }}</template>
                </p>
              </div>
              <div class="security-action">
                <el-button
                    type="primary"
                    :disabled="!userInfo.mobile || userInfo.mobileConfirmed"
                    @click="showPhoneVerifyDialog"
                >
                  {{ t('profile.phonebindBtn') }}
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 验证码对话框 -->
    <el-dialog
        :title="$t('profile.verificationcode')"
        v-model="captchaDialogVisible"
        width="400px"
        append-to-body
    >
      <div class="captcha-container">
        <div class="captcha-image" v-if="captcha">
          <img
              :src="'data:image/png;base64,' + captcha.image"
              alt="验证码"
              @click="refreshCaptcha"
          />
          <p>点击图片刷新验证码</p>
        </div>
        <el-form
            ref="captchaFormRef"
            :model="captchaForm"
            :rules="captchaRules"
            label-width="80px"
        >
          <el-form-item :label="$t('profile.verificationcode')" prop="code">
            <el-input v-model="captchaForm.code" placeholder="请输入验证码"/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="captchaDialogVisible = false">{{ t('cancel') }}</el-button>
          <el-button type="primary" @click="submitCaptcha">{{ t('confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 邮箱验证对话框 -->
    <el-dialog
        title="验证邮箱"
        v-model="emailVerifyDialogVisible"
        width="500px"
        append-to-body
    >
      <el-form
          ref="emailVerifyFormRef"
          :model="emailVerifyForm"
          :rules="emailVerifyRules"
          label-width="100px"
      >
        <el-form-item label="邮箱地址" prop="email">
          <el-input
              v-model="emailVerifyForm.email"
              placeholder="请输入邮箱地址"
              disabled
          />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <div class="verification-code-input">
            <el-input
                v-model="emailVerifyForm.code"
                placeholder="请输入验证码"
            />
            <el-button
                type="primary"
                @click="sendEmailCode"
                :disabled="emailCodeSending || emailCountdown > 0"
            >
              {{
                emailCountdown > 0 ? `${emailCountdown}秒后重试` : "获取验证码"
              }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="emailVerifyDialogVisible = false">{{ t('cancel') }}</el-button>
          <el-button
              type="primary"
              @click="submitEmailVerify"
              :loading="emailVerifySubmitting"
          >{{ t('confirm') }}
          </el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 手机验证对话框 -->
    <el-dialog
        title="验证手机"
        v-model="phoneVerifyDialogVisible"
        width="500px"
        append-to-body
    >
      <el-form
          ref="phoneVerifyFormRef"
          :model="phoneVerifyForm"
          :rules="phoneVerifyRules"
          label-width="100px"
      >
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input
              v-model="phoneVerifyForm.phoneNumber"
              placeholder="请输入手机号码"
              disabled
          />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <div class="verification-code-input">
            <el-input
                v-model="phoneVerifyForm.code"
                placeholder="请输入验证码"
            />
            <el-button
                type="primary"
                @click="sendPhoneCode"
                :disabled="phoneCodeSending || phoneCountdown > 0"
            >
              {{
                phoneCountdown > 0 ? `${phoneCountdown}秒后重试` : "获取验证码"
              }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="phoneVerifyDialogVisible = false">{{ t('cancel') }}</el-button>
          <el-button
              type="primary"
              @click="submitPhoneVerify"
              :loading="phoneVerifySubmitting"
          >{{ t('confirm') }}
          </el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from "vue";
import {ElMessage, FormInstance} from "element-plus";
import {UserType, UserStatus} from "@/enums";
import {
  getUserInfo,
  updateProfile as updateProfileApi,
  getCaptcha,
  sendEmailConfirmationCode,
  confirmEmail,
  sendPhoneConfirmationCode,
  confirmPhone,
} from "@/api/rbac";
import {type UserInfo, type UpdateProfileParams} from "@/dtos";
import FileUploader from "@/components/common/FileUploader.vue";
import ImagePreview from "@/components/common/ImagePreview.vue";
import {useI18n} from "vue-i18n";

const base = import.meta.env.BASE_URL;

const {t} = useI18n();
// 用户信息
const userInfo = ref<UserInfo>({
  key: "0",
  username: "",
  realName: "",
  email: "",
  mobile: "",
  avatar: {},
  userType: UserType.Internal,
  status: UserStatus.Enabled,
  gender: "",
  country: "",
  unit: "",
  contactAddress: "",
  telephone: "",
});

// 加载状态
const loading = ref(false);
const originalUserInfo = ref<UserInfo | null>(null);

// 验证码相关
const captchaDialogVisible = ref(false);
const captcha = ref<{ id: string; image: string; expiresIn: number } | null>(
    null
);
const captchaForm = reactive({
  code: "",
});
const captchaFormRef = ref<FormInstance>();
const captchaRules = {
  code: [{required: true, message: "请输入验证码", trigger: "blur"}],
};
const captchaAction = ref<string>("");

// 邮箱验证相关
const emailVerifyDialogVisible = ref(false);
const emailVerifyForm = reactive({
  email: "",
  code: "",
});
const emailVerifyFormRef = ref<FormInstance>();
const emailVerifyRules = {
  email: [
    {required: true, message: "请输入邮箱地址", trigger: "blur"},
    {
      validator: (_rule: any, value: string, callback: any) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!value || emailRegex.test(value)) {
          callback();
        } else {
          callback(new Error("请输入正确的邮箱地址"));
        }
      },
      trigger: "blur",
    },
  ],
  code: [{required: true, message: "请输入验证码", trigger: "blur"}],
};
const emailCodeSending = ref(false);
const emailCountdown = ref(0);
const emailVerifySubmitting = ref(false);

// 手机验证相关
const phoneVerifyDialogVisible = ref(false);
const phoneVerifyForm = reactive({
  phoneNumber: "",
  code: "",
});
const phoneVerifyFormRef = ref<FormInstance>();
const phoneVerifyRules = {
  phoneNumber: [
    {required: true, message: "请输入手机号码", trigger: "blur"},
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号",
      trigger: "blur",
    },
  ],
  code: [{required: true, message: "请输入验证码", trigger: "blur"}],
};
const phoneCodeSending = ref(false);
const phoneCountdown = ref(0);
const phoneVerifySubmitting = ref(false);

// 获取用户信息
const fetchUserInfo = async () => {
  loading.value = true;
  try {
    const {data} = await getUserInfo();
    userInfo.value = data;
    originalUserInfo.value = data;
  } catch (error) {
    console.error("获取用户信息失败:", error);
  } finally {
    loading.value = false;
  }
};

// 更新个人资料
const updateProfile = async () => {
  try {
    const params: UpdateProfileParams = {
      realName: userInfo.value.realName,
      gender: userInfo.value.gender,
      country: userInfo.value.country,
      unit: userInfo.value.unit,
      contactAddress: userInfo.value.contactAddress,
      telephone: userInfo.value.telephone,
    };

    await updateProfileApi(params);
    ElMessage.success("个人资料更新成功/Profile updated successfully");
    await fetchUserInfo(); // 刷新用户信息
  } catch (error) {
    console.error("更新个人资料失败:", error);
    ElMessage.error("更新个人资料失败/Update profile failed");
  }
};

// 重置表单
const resetForm = () => {
  if (originalUserInfo.value) {
    userInfo.value = JSON.parse(JSON.stringify(originalUserInfo.value));
  }
};

// 获取验证码
const fetchCaptcha = async () => {
  try {
    const {data} = await getCaptcha();
    captcha.value = data;
  } catch (error) {
    console.error("获取验证码失败:", error);
    ElMessage.error("获取验证码失败/Failed to get captcha");
  }
};

// 刷新验证码
const refreshCaptcha = async () => {
  await fetchCaptcha();
};

// 显示验证码对话框
const showCaptchaDialog = (action: string) => {
  captchaAction.value = action;
  captchaForm.code = "";
  captchaDialogVisible.value = true;
  fetchCaptcha();
};

// 提交验证码
const submitCaptcha = async () => {
  if (!captchaFormRef.value) return;

  await captchaFormRef.value.validate(async (valid) => {
    if (valid && captcha.value) {
      captchaDialogVisible.value = false;

      if (captchaAction.value === "email") {
        await sendEmailVerificationCode();
      } else if (captchaAction.value === "phone") {
        await sendPhoneVerificationCode();
      }
    }
  });
};

// 显示邮箱验证对话框
const showEmailVerifyDialog = () => {
  emailVerifyForm.email = userInfo.value.email || "";
  emailVerifyForm.code = "";
  emailVerifyDialogVisible.value = true;
  emailCountdown.value = 0;
};

// 发送邮箱验证码
const sendEmailCode = () => {
  showCaptchaDialog("email");
};

// 发送邮箱验证码（验证码通过后）
const sendEmailVerificationCode = async () => {
  if (!captcha.value) return;

  try {
    emailCodeSending.value = true;
    await sendEmailConfirmationCode({
      email: emailVerifyForm.email,
      captchaId: captcha.value.id,
      captchaCode: captchaForm.code,
    });

    ElMessage.success("验证码已发送到您的邮箱/Verification code has been sent to your email");

    // 开始倒计时
    emailCountdown.value = 60;
    const timer = setInterval(() => {
      emailCountdown.value--;
      if (emailCountdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error: any) {
    ElMessage.error(error.message || "发送验证码失败/Failed to send verification code");
  } finally {
    emailCodeSending.value = false;
  }
};

// 提交邮箱验证
const submitEmailVerify = async () => {
  if (!emailVerifyFormRef.value) return;

  await emailVerifyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        emailVerifySubmitting.value = true;
        await confirmEmail({
          email: emailVerifyForm.email,
          code: emailVerifyForm.code,
        });

        ElMessage.success("邮箱验证成功/Email verification successful");
        emailVerifyDialogVisible.value = false;
        await fetchUserInfo(); // 刷新用户信息
      } catch (error: any) {
        ElMessage.error(error.message || "邮箱验证失败/Email verification failed");
      } finally {
        emailVerifySubmitting.value = false;
      }
    }
  });
};

// 显示手机验证对话框
const showPhoneVerifyDialog = () => {
  phoneVerifyForm.phoneNumber = userInfo.value.mobile || "";
  phoneVerifyForm.code = "";
  phoneVerifyDialogVisible.value = true;
  phoneCountdown.value = 0;
};

// 发送手机验证码
const sendPhoneCode = () => {
  showCaptchaDialog("phone");
};

// 发送手机验证码（验证码通过后）
const sendPhoneVerificationCode = async () => {
  if (!captcha.value) return;

  try {
    phoneCodeSending.value = true;
    await sendPhoneConfirmationCode({
      phoneNumber: phoneVerifyForm.phoneNumber,
      captchaId: captcha.value.id,
      captchaCode: captchaForm.code,
    });

    ElMessage.success("验证码已发送到您的手机/Verification code has been sent to your phone");

    // 开始倒计时
    phoneCountdown.value = 60;
    const timer = setInterval(() => {
      phoneCountdown.value--;
      if (phoneCountdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error: any) {
    ElMessage.error(error.message || "发送验证码失败/Failed to send verification code");
  } finally {
    phoneCodeSending.value = false;
  }
};

// 提交手机验证
const submitPhoneVerify = async () => {
  if (!phoneVerifyFormRef.value) return;

  await phoneVerifyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        phoneVerifySubmitting.value = true;
        await confirmPhone({
          phoneNumber: phoneVerifyForm.phoneNumber,
          code: phoneVerifyForm.code,
        });

        ElMessage.success("手机验证成功/Phone verification successful");
        phoneVerifyDialogVisible.value = false;
        await fetchUserInfo(); // 刷新用户信息
      } catch (error: any) {
        ElMessage.error(error.message || "手机验证失败/Phone verification failed");
      } finally {
        phoneVerifySubmitting.value = false;
      }
    }
  });
};

async function updateProfileAvatar(fileInfo: any) {
  try {
    await updateProfileApi({
      realName: userInfo.value.realName,
      avatar: fileInfo,
    });
    ElMessage.success("头像更新成功/Avatar updated successfully");
    await fetchUserInfo();
  } catch (error) {
    ElMessage.error("头像更新失败/Avatar update failed");
  }
}

function onAvatarUpload(files: any[]) {
  if (files && files.length > 0) {
    userInfo.value.avatar = files[0];
    updateProfileAvatar(files[0]);
  }
}

// 页面加载时获取用户信息
onMounted(() => {
  fetchUserInfo();
});
</script>

<style lang="scss" scoped>
.profile-card,
.security-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.profile-content {
  display: flex;
  flex-direction: column;

  @media (min-width: 768px) {
    flex-direction: row;
  }

  .profile-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 30px;
    margin-bottom: 20px;

    .avatar-actions {
      margin-top: 10px;
    }
  }

  .profile-info {
    flex: 1;
  }
}

.security-content {
  .security-item {
    //display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;

    .security-info {
      margin: 10px;

      h3 {
        margin: 0 0 5px 0;
        font-size: 16px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;

        .el-tag {
          margin-left: 5px;
        }
      }
    }

    .security-action {
      .el-button {
        margin-left: 10px;
      }
    }
  }
}

.captcha-container {
  .captcha-image {
    text-align: center;
    margin-bottom: 20px;

    img {
      cursor: pointer;
      max-width: 100%;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
    }

    p {
      margin-top: 5px;
      color: #909399;
      font-size: 12px;
    }
  }
}

.verification-code-input {
  display: flex;

  .el-input {
    flex: 1;
    margin-right: 10px;
  }
}
</style>
