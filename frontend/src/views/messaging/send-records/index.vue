<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <SearchForm
      :form-items="searchFormItems"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      title="发送记录管理"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      :total="total"
      :current-page-prop="queryParams.$pageIndex"
      :page-size-prop="queryParams.$pageSize"
      @page-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <!-- 可以在这里添加工具栏按钮 -->
      </template>

      <!-- 消息类型列 -->
      <template #messageType="{ row }">
        <el-tag>{{ getMessageTypeLabel(row.messageType) }}</el-tag>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getMessageStatusTagType(row.status)">
          {{ getMessageStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 发送时间列 -->
      <template #sendTime="{ row }">
        {{ row.sendTime ? formatDateTimeOffsetToLocalDateTime(row.sendTime) : '-' }}
      </template>



      <!-- 创建时间列 -->
      <template #createdTime="{ row }">
        {{ formatDateTimeOffsetToLocalDateTime(row.createdTime) }}
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleView(row)">查看</el-button>
      </template>
    </DataTable>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="消息发送详情"
      width="1000px"
      append-to-body
    >
      <el-descriptions v-if="currentRecord" :column="2" border label-width="180">
        <el-descriptions-item label="ID">{{ currentRecord.key }}</el-descriptions-item>
        <el-descriptions-item label="应用编码">{{ currentRecord.appCode }}</el-descriptions-item>
        <el-descriptions-item label="消息类型">
          <el-tag>{{ getMessageTypeLabel(currentRecord.messageType) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发送目标">{{ currentRecord.target }}</el-descriptions-item>
        <el-descriptions-item label="模板编码">{{ currentRecord.templateCode }}</el-descriptions-item>
        <el-descriptions-item label="服务商">{{ currentRecord.providerCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="发送状态">
          <el-tag :type="getMessageStatusTagType(currentRecord.status)">
            {{ getMessageStatusLabel(currentRecord.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="重试次数">{{ currentRecord.tryTime }}</el-descriptions-item>
        <el-descriptions-item label="发送时间">
          {{ currentRecord.sendTime ? formatDateTimeOffsetToLocalDateTime(currentRecord.sendTime) : '-' }}
        </el-descriptions-item>

        <el-descriptions-item label="创建时间">
          {{ formatDateTimeOffsetToLocalDateTime(currentRecord.createdTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="变量JSON" :span="2">
          <pre v-if="currentRecord.variablesJson">{{ formatJson(currentRecord.variablesJson) }}</pre>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="currentRecord.errorInfo" label="错误信息" :span="2">
          <div style="color: #f56c6c; max-height: 200px; overflow-y: auto;">
            {{ currentRecord.errorInfo }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import SearchForm, { FormItem } from '@/components/common/SearchForm.vue'
import DataTable, { TableColumn } from '@/components/common/DataTable.vue'
import { messageSendApi } from '@/api/messaging-mgt'
import type { MessageSendDto, MessageSendQueryCriteria } from '@/dtos/messaging-mgt.dto'
import {
  MESSAGE_TYPE_OPTIONS,
  MESSAGE_STATUS_OPTIONS,
  getMessageTypeLabel,
  getMessageStatusLabel,
  getMessageStatusTagType
} from '@/enums/messaging'
import { formatDateTimeOffsetToLocalDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const total = ref(0)
const tableData = ref<MessageSendDto[]>([])
const detailVisible = ref(false)
const currentRecord = ref<MessageSendDto>()

// 搜索表单项配置
const searchFormItems = computed(() => [
  {
    type: "input",
    label: "应用编码",
    prop: "appCode",
    placeholder: "请输入应用编码",
  },
  {
    type: "select",
    label: "消息类型",
    prop: "messageType",
    placeholder: "请选择消息类型",
    options: MESSAGE_TYPE_OPTIONS,
  },
  {
    type: "select",
    label: "发送状态",
    prop: "status",
    placeholder: "请选择发送状态",
    options: MESSAGE_STATUS_OPTIONS,
  },
  {
    type: "input",
    label: "模板编码",
    prop: "templateCode",
    placeholder: "请输入模板编码",
  },
  {
    type: "input",
    label: "发送目标",
    prop: "target",
    placeholder: "请输入发送目标",
  },
  {
    type: "datetime",
    label: "时间范围",
    prop: "dateRange",
    dateType: "datetimerange",
    placeholder: "请选择时间范围",
    format: "YYYY-MM-DD HH:mm:ss",
    valueFormat: "YYYY-MM-DD HH:mm:ss",
  },
] as FormItem[])

// 表格列配置
const tableColumns = computed(() => [
  { prop: "key", label: "ID", width: 80 },
  { prop: "appCode", label: "应用编码", width: 120 },
  { prop: "messageType", label: "消息类型", slot: "messageType", width: 100 },
  { prop: "target", label: "发送目标", width: 150 },
  { prop: "templateCode", label: "模板编码", width: 150 },
  { prop: "status", label: "状态", slot: "status", width: 100 },
  { prop: "providerCode", label: "服务商", width: 120 },
  { prop: "tryTime", label: "重试次数", width: 100 },
  { prop: "sendTime", label: "发送时间", slot: "sendTime", width: 180 },
  { prop: "createdTime", label: "创建时间", slot: "createdTime", width: 180 },
] as TableColumn[])

// 查询参数
const queryParams = reactive<MessageSendQueryCriteria>({
  $pageIndex: 1,
  $pageSize: 20,
  appCode: undefined,
  messageType: undefined,
  status: undefined,
  templateCode: undefined,
  target: undefined,
  startTime: undefined,
  endTime: undefined,
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 处理时间范围
    if (queryParams.dateRange && Array.isArray(queryParams.dateRange)) {
      queryParams.startTime = queryParams.dateRange[0]
      queryParams.endTime = queryParams.dateRange[1]
    } else {
      queryParams.startTime = undefined
      queryParams.endTime = undefined
    }

    const { data } = await messageSendApi.getPage(queryParams)
    tableData.value = data.rows
    total.value = data.totals
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData)
  queryParams.$pageIndex = 1
  getList()
}

// 重置处理
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData)
  getList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val
  getList()
}

// 查看详情
const handleView = (row: MessageSendDto) => {
  currentRecord.value = row
  detailVisible.value = true
}

// 格式化JSON
const formatJson = (jsonStr: string) => {
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 初始化
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}
</style>
