<template>
  <div class="watermark-strength-test">
    <el-page-header @back="$router.go(-1)" content="水印强度测试" />
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 左侧：测试配置 -->
      <el-col :span="8">
        <el-card header="测试配置">
          <el-form :model="testConfig" label-width="100px">
            <el-form-item label="测试图片">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :show-file-list="false"
                accept="image/*"
                @change="handleImageUpload"
              >
                <el-button type="primary" :icon="UploadFilled">选择图片</el-button>
              </el-upload>
              <div v-if="originalImage" style="margin-top: 8px;">
                <el-tag type="success">已选择图片</el-tag>
              </div>
            </el-form-item>

            <el-form-item label="压缩质量">
              <el-slider 
                v-model="testConfig.compressionQuality" 
                :min="10" 
                :max="100" 
                :step="10"
                show-stops
                show-input
              />
            </el-form-item>

            <el-form-item label="缩放比例">
              <el-slider 
                v-model="testConfig.scaleRatio" 
                :min="0.1" 
                :max="2.0" 
                :step="0.1"
                show-input
              />
            </el-form-item>

            <el-form-item label="噪声强度">
              <el-slider 
                v-model="testConfig.noiseLevel" 
                :min="0" 
                :max="50" 
                :step="5"
                show-stops
                show-input
              />
            </el-form-item>

            <el-form-item label="模糊程度">
              <el-slider 
                v-model="testConfig.blurRadius" 
                :min="0" 
                :max="10" 
                :step="1"
                show-stops
                show-input
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="runStrengthTest" :loading="testing" :disabled="!originalImage">
                开始强度测试
              </el-button>
              <el-button @click="resetTest" :disabled="testing">
                重置测试
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 测试进度 -->
        <el-card v-if="testing || testResults.length > 0" header="测试进度" style="margin-top: 20px;">
          <el-progress 
            :percentage="testProgress" 
            :status="testing ? 'active' : 'success'"
            :stroke-width="8"
          />
          <div style="margin-top: 8px; text-align: center;">
            {{ currentTestStep }}
          </div>
        </el-card>
      </el-col>

      <!-- 中间：图像预览 -->
      <el-col :span="8">
        <el-card header="图像预览">
          <div class="image-preview">
            <div v-if="!originalImage" class="placeholder">
              <el-icon size="64" color="#ccc"><Picture /></el-icon>
              <p>请选择测试图片</p>
            </div>
            <div v-else>
              <h4>原始图片</h4>
              <canvas ref="originalCanvas" class="preview-canvas"></canvas>
              
              <h4 v-if="processedImageUrl" style="margin-top: 16px;">处理后图片</h4>
              <img v-if="processedImageUrl" :src="processedImageUrl" class="preview-canvas" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：测试结果 -->
      <el-col :span="8">
        <el-card header="测试结果">
          <div v-if="testResults.length === 0" class="placeholder">
            <el-icon size="48" color="#ccc"><DataAnalysis /></el-icon>
            <p>暂无测试结果</p>
          </div>
          
          <div v-else>
            <el-table :data="testResults" size="small" max-height="400">
              <el-table-column prop="testName" label="测试项" width="120" />
              <el-table-column prop="confidence" label="置信度" width="80">
                <template #default="{ row }">
                  <el-tag 
                    :type="row.confidence > 0.7 ? 'success' : row.confidence > 0.4 ? 'warning' : 'danger'"
                    size="small"
                  >
                    {{ (row.confidence * 100).toFixed(1) }}%
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-icon :color="row.success ? '#67c23a' : '#f56c6c'">
                    <Check v-if="row.success" />
                    <Close v-else />
                  </el-icon>
                </template>
              </el-table-column>
            </el-table>

            <!-- 总体评分 -->
            <div style="margin-top: 16px; padding: 16px; background: #f5f7fa; border-radius: 4px;">
              <h4>总体评分</h4>
              <el-progress 
                :percentage="overallScore" 
                :color="overallScore > 70 ? '#67c23a' : overallScore > 40 ? '#e6a23c' : '#f56c6c'"
                :stroke-width="12"
              />
              <p style="margin-top: 8px; text-align: center;">
                <strong>{{ getScoreDescription(overallScore) }}</strong>
              </p>
            </div>

            <!-- 建议 -->
            <div style="margin-top: 16px;">
              <h4>优化建议</h4>
              <ul style="margin: 0; padding-left: 20px;">
                <li v-for="suggestion in suggestions" :key="suggestion">{{ suggestion }}</li>
              </ul>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细测试报告 -->
    <el-row v-if="testResults.length > 0" :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="详细测试报告">
          <el-table :data="testResults" border>
            <el-table-column prop="testName" label="测试项" width="150" />
            <el-table-column prop="description" label="测试描述" />
            <el-table-column prop="confidence" label="置信度" width="100">
              <template #default="{ row }">
                {{ (row.confidence * 100).toFixed(1) }}%
              </template>
            </el-table-column>
            <el-table-column prop="extractionMethod" label="提取方法" width="120" />
            <el-table-column prop="processingTime" label="处理时间" width="100">
              <template #default="{ row }">
                {{ row.processingTime }}ms
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.success ? 'success' : 'danger'" size="small">
                  {{ row.success ? '通过' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Picture, DataAnalysis, Check, Close } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { AdvancedWatermarkExtractor, type AdvancedExtractionResult } from '@/utils/advanced-watermark-extractor'

// 响应式数据
const originalCanvas = ref<HTMLCanvasElement>()
const originalImage = ref<HTMLImageElement | null>(null)
const processedImageUrl = ref<string>('')
const testing = ref(false)
const testProgress = ref(0)
const currentTestStep = ref('')

// 测试配置
const testConfig = ref({
  compressionQuality: 80,
  scaleRatio: 1.0,
  noiseLevel: 10,
  blurRadius: 2
})

// 测试结果
const testResults = ref<Array<{
  testName: string
  description: string
  success: boolean
  confidence: number
  extractionMethod?: string
  processingTime: number
  error?: string
}>>([])

// 高级提取器
const advancedExtractor = new AdvancedWatermarkExtractor()

// 计算属性
const overallScore = computed(() => {
  if (testResults.value.length === 0) return 0
  
  const totalScore = testResults.value.reduce((sum, result) => {
    return sum + (result.success ? result.confidence * 100 : 0)
  }, 0)
  
  return Math.round(totalScore / testResults.value.length)
})

const suggestions = computed(() => {
  const suggestions: string[] = []
  
  if (overallScore.value < 40) {
    suggestions.push('水印强度较弱，建议增加水印密度或调整嵌入算法')
  }
  
  const compressionTest = testResults.value.find(r => r.testName.includes('压缩'))
  if (compressionTest && !compressionTest.success) {
    suggestions.push('水印对压缩敏感，建议使用频域嵌入方法')
  }
  
  const scaleTest = testResults.value.find(r => r.testName.includes('缩放'))
  if (scaleTest && !scaleTest.success) {
    suggestions.push('水印对缩放敏感，建议使用尺度不变特征')
  }
  
  const noiseTest = testResults.value.find(r => r.testName.includes('噪声'))
  if (noiseTest && !noiseTest.success) {
    suggestions.push('水印对噪声敏感，建议增强错误纠正能力')
  }
  
  if (suggestions.length === 0) {
    suggestions.push('水印强度良好，继续保持当前配置')
  }
  
  return suggestions
})

// 处理图像上传
const handleImageUpload = (file: UploadFile) => {
  if (!file.raw) return

  const img = new Image()
  img.onload = () => {
    originalImage.value = img
    nextTick(() => {
      drawOriginalImage()
    })
  }
  img.src = URL.createObjectURL(file.raw)
}

// 绘制原始图像
const drawOriginalImage = () => {
  if (!originalCanvas.value || !originalImage.value) return

  const canvas = originalCanvas.value
  const ctx = canvas.getContext('2d')!
  
  // 设置画布尺寸
  const maxSize = 300
  const { width, height } = originalImage.value
  const scale = Math.min(maxSize / width, maxSize / height)
  
  canvas.width = width * scale
  canvas.height = height * scale
  
  ctx.drawImage(originalImage.value, 0, 0, canvas.width, canvas.height)
}

// 获取评分描述
const getScoreDescription = (score: number): string => {
  if (score >= 80) return '优秀 - 水印强度很高'
  if (score >= 60) return '良好 - 水印强度较高'
  if (score >= 40) return '一般 - 水印强度中等'
  return '较差 - 水印强度较低'
}

// 运行强度测试
const runStrengthTest = async () => {
  if (!originalImage.value) {
    ElMessage.warning('请先选择测试图片')
    return
  }

  testing.value = true
  testProgress.value = 0
  testResults.value = []
  currentTestStep.value = '准备测试...'

  const tests = [
    { name: 'JPEG压缩测试', description: '测试水印在JPEG压缩后的存活性', test: testJpegCompression },
    { name: '图像缩放测试', description: '测试水印在图像缩放后的存活性', test: testImageScaling },
    { name: '噪声干扰测试', description: '测试水印在添加噪声后的存活性', test: testNoiseInterference },
    { name: '模糊处理测试', description: '测试水印在模糊处理后的存活性', test: testBlurProcessing },
    { name: '原始图像测试', description: '测试原始图像中的水印提取', test: testOriginalImage }
  ]

  for (let i = 0; i < tests.length; i++) {
    const test = tests[i]
    currentTestStep.value = `正在执行: ${test.name}`
    testProgress.value = (i / tests.length) * 100

    try {
      const result = await test.test()
      testResults.value.push({
        testName: test.name,
        description: test.description,
        ...result
      })
    } catch (error) {
      testResults.value.push({
        testName: test.name,
        description: test.description,
        success: false,
        confidence: 0,
        processingTime: 0,
        error: error instanceof Error ? error.message : '未知错误'
      })
    }

    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟处理时间
  }

  testProgress.value = 100
  currentTestStep.value = '测试完成'
  testing.value = false
  
  ElMessage.success('强度测试完成')
}

// 测试方法实现
const testJpegCompression = async () => {
  const startTime = Date.now()
  const compressedImage = await compressImage(originalImage.value!, testConfig.value.compressionQuality)
  const result = await advancedExtractor.extractFromImage(compressedImage)
  
  processedImageUrl.value = compressedImage.src
  
  return {
    success: result.success,
    confidence: result.confidence,
    extractionMethod: result.extractionMethod,
    processingTime: Date.now() - startTime
  }
}

const testImageScaling = async () => {
  const startTime = Date.now()
  const scaledImage = await scaleImage(originalImage.value!, testConfig.value.scaleRatio)
  const result = await advancedExtractor.extractFromImage(scaledImage)
  
  return {
    success: result.success,
    confidence: result.confidence,
    extractionMethod: result.extractionMethod,
    processingTime: Date.now() - startTime
  }
}

const testNoiseInterference = async () => {
  const startTime = Date.now()
  const noisyImage = await addNoise(originalImage.value!, testConfig.value.noiseLevel)
  const result = await advancedExtractor.extractFromImage(noisyImage)
  
  return {
    success: result.success,
    confidence: result.confidence,
    extractionMethod: result.extractionMethod,
    processingTime: Date.now() - startTime
  }
}

const testBlurProcessing = async () => {
  const startTime = Date.now()
  const blurredImage = await blurImage(originalImage.value!, testConfig.value.blurRadius)
  const result = await advancedExtractor.extractFromImage(blurredImage)
  
  return {
    success: result.success,
    confidence: result.confidence,
    extractionMethod: result.extractionMethod,
    processingTime: Date.now() - startTime
  }
}

const testOriginalImage = async () => {
  const startTime = Date.now()
  const result = await advancedExtractor.extractFromImage(originalImage.value!)
  
  return {
    success: result.success,
    confidence: result.confidence,
    extractionMethod: result.extractionMethod,
    processingTime: Date.now() - startTime
  }
}

// 图像处理辅助函数
const compressImage = (img: HTMLImageElement, quality: number): Promise<HTMLImageElement> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = img.width
    canvas.height = img.height
    ctx.drawImage(img, 0, 0)
    
    const dataUrl = canvas.toDataURL('image/jpeg', quality / 100)
    
    const newImg = new Image()
    newImg.onload = () => resolve(newImg)
    newImg.src = dataUrl
  })
}

const scaleImage = (img: HTMLImageElement, scale: number): Promise<HTMLImageElement> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = img.width * scale
    canvas.height = img.height * scale
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
    
    const dataUrl = canvas.toDataURL('image/png')
    
    const newImg = new Image()
    newImg.onload = () => resolve(newImg)
    newImg.src = dataUrl
  })
}

const addNoise = (img: HTMLImageElement, noiseLevel: number): Promise<HTMLImageElement> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = img.width
    canvas.height = img.height
    ctx.drawImage(img, 0, 0)
    
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data
    
    for (let i = 0; i < data.length; i += 4) {
      const noise = (Math.random() - 0.5) * noiseLevel * 2
      data[i] = Math.max(0, Math.min(255, data[i] + noise))
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise))
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise))
    }
    
    ctx.putImageData(imageData, 0, 0)
    
    const dataUrl = canvas.toDataURL('image/png')
    
    const newImg = new Image()
    newImg.onload = () => resolve(newImg)
    newImg.src = dataUrl
  })
}

const blurImage = (img: HTMLImageElement, radius: number): Promise<HTMLImageElement> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = img.width
    canvas.height = img.height
    ctx.filter = `blur(${radius}px)`
    ctx.drawImage(img, 0, 0)
    
    const dataUrl = canvas.toDataURL('image/png')
    
    const newImg = new Image()
    newImg.onload = () => resolve(newImg)
    newImg.src = dataUrl
  })
}

// 重置测试
const resetTest = () => {
  testResults.value = []
  testProgress.value = 0
  currentTestStep.value = ''
  processedImageUrl.value = ''
}
</script>

<style lang="scss" scoped>
.watermark-strength-test {
  padding: 20px;
}

.image-preview {
  text-align: center;
  
  .placeholder {
    padding: 40px;
    color: #909399;
    
    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  
  .preview-canvas {
    max-width: 100%;
    max-height: 300px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
  
  h4 {
    margin: 16px 0 8px 0;
    color: #303133;
  }
}

:deep(.el-slider__input) {
  width: 80px;
}
</style>
