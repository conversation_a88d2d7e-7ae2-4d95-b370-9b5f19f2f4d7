<template>
  <div class="watermark-analyzer">
    <el-page-header @back="$router.go(-1)" content="水印分析工具" />
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 图像处理工具 -->
      <el-col :span="24">
        <el-card header="图像处理">
          <div v-if="!originalImage" class="upload-area">
            <el-upload
                drag
                :auto-upload="false"
                :on-change="handleImageUpload"
                :show-file-list="false"
                accept="image/*"
            >
              <el-icon class="el-icon--upload">
                <upload-filled/>
              </el-icon>
              <div class="el-upload__text">上传截图进行分析</div>
            </el-upload>
          </div>

          <div v-else class="image-processing">
            <!-- 图像显示 -->
            <div class="image-section">
              <h4>图像预览</h4>
              <canvas ref="originalCanvas" class="image-canvas"></canvas>
              <canvas ref="processedCanvas" class="image-canvas" style="margin-left: 20px;"></canvas>
            </div>

            <!-- 处理控制 -->
            <div class="controls-section" style="margin-top: 20px;">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="亮度">
                    <el-slider v-model="brightness" :min="-100" :max="100" @change="processImage"/>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="对比度">
                    <el-slider v-model="contrast" :min="0" :max="300" @change="processImage"/>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="饱和度">
                    <el-slider v-model="saturation" :min="0" :max="200" @change="processImage"/>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="伽马">
                    <el-slider v-model="gamma" :min="0.1" :max="3" :step="0.1" @change="processImage"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 操作按钮 -->
            <div style="margin-top: 16px; text-align: center;">
              <el-space>
                <el-button type="warning" @click="performEnhancedExtraction" :disabled="!processedCanvas" :loading="isExtracting">
                  提取水印
                </el-button>
                <el-button type="info" @click="visualizeWatermark" :disabled="!originalImage">
                  可视化增强
                </el-button>
                <el-button @click="resetControls">重置</el-button>
              </el-space>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 提取结果显示 -->
    <el-row v-if="enhancedResult" :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>水印提取结果</span>
              <el-tag :type="enhancedResult.success ? 'success' : 'danger'">
                {{ enhancedResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>
          
          <div v-if="enhancedResult.success">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="置信度">
                <el-progress 
                  :percentage="Math.round(enhancedResult.confidence * 100)" 
                  :color="enhancedResult.confidence > 0.7 ? '#67c23a' : enhancedResult.confidence > 0.4 ? '#e6a23c' : '#f56c6c'"
                />
              </el-descriptions-item>
              <el-descriptions-item label="提取方法">
                {{ enhancedResult.extractionMethod }}
              </el-descriptions-item>
              <el-descriptions-item label="用户" v-if="enhancedResult.watermarkData?.user">
                {{ enhancedResult.watermarkData.user }}
              </el-descriptions-item>
              <el-descriptions-item label="时间戳" v-if="enhancedResult.watermarkData?.timestamp">
                {{ new Date(enhancedResult.watermarkData.timestamp).toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="路径" v-if="enhancedResult.watermarkData?.path">
                {{ enhancedResult.watermarkData.path }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-else>
            <el-alert 
              :title="enhancedResult.error || '未知错误'" 
              type="warning" 
              :closable="false"
              show-icon
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 水印测试 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="水印测试">
          <div style="text-align: center;">
            <el-space>
              <el-button type="primary" @click="startWatermarkTest" :disabled="watermarkActive">
                启动测试水印
              </el-button>
              <el-button type="danger" @click="stopWatermarkTest" :disabled="!watermarkActive">
                停止水印
              </el-button>
              <el-tag :type="watermarkActive ? 'success' : 'info'">
                {{ watermarkActive ? '运行中' : '已停止' }}
              </el-tag>
            </el-space>
            <div style="margin-top: 12px; font-size: 12px; color: #666;">
              当前用户: {{ userStore.name || '未知用户' }} | 页面: {{ route.path }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { EnhancedWatermarkExtractor, type ExtractionResult } from '@/utils/enhanced-watermark-extractor'
import { WatermarkComposer } from '@/utils/watermark-composer'
import { WatermarkVisualizer } from '@/utils/watermark-visualizer'

// 基础数据
const route = useRoute()
const userStore = useUserStore()

// 设置全局用户信息供提取器使用
;(window as any).__USER_STORE__ = userStore

// 响应式数据
const originalCanvas = ref<HTMLCanvasElement>()
const processedCanvas = ref<HTMLCanvasElement>()
const originalImage = ref<HTMLImageElement | null>(null)

// 图像处理参数
const brightness = ref(0)
const contrast = ref(100)
const saturation = ref(100)
const gamma = ref(1.0)

// 水印提取相关
const isExtracting = ref(false)
const enhancedExtractor = new EnhancedWatermarkExtractor()
const enhancedResult = ref<ExtractionResult | null>(null)

// 水印测试相关
let watermarkComposer: WatermarkComposer | null = null
const watermarkActive = ref(false)

// 水印可视化相关
const visualizer = new WatermarkVisualizer()

// 处理图像上传
const handleImageUpload = (file: UploadFile) => {
  if (!file.raw) return

  const img = new Image()
  img.onload = () => {
    originalImage.value = img
    nextTick(() => {
      drawOriginalImage()
      processImage()
    })
  }
  img.src = URL.createObjectURL(file.raw)
}

// 绘制原始图像
const drawOriginalImage = () => {
  if (!originalCanvas.value || !originalImage.value) return

  const canvas = originalCanvas.value
  const ctx = canvas.getContext('2d')!
  
  canvas.width = Math.min(originalImage.value.width, 400)
  canvas.height = Math.min(originalImage.value.height, 300)
  
  ctx.drawImage(originalImage.value, 0, 0, canvas.width, canvas.height)
}

// 处理图像
const processImage = () => {
  if (!originalCanvas.value || !processedCanvas.value || !originalImage.value) return

  const srcCanvas = originalCanvas.value
  const dstCanvas = processedCanvas.value
  const ctx = dstCanvas.getContext('2d')!
  
  dstCanvas.width = srcCanvas.width
  dstCanvas.height = srcCanvas.height
  
  ctx.drawImage(srcCanvas, 0, 0)
  
  const imageData = ctx.getImageData(0, 0, dstCanvas.width, dstCanvas.height)
  const data = imageData.data
  
  for (let i = 0; i < data.length; i += 4) {
    let r = data[i]
    let g = data[i + 1]
    let b = data[i + 2]
    
    // 亮度调整
    r += brightness.value * 2.55
    g += brightness.value * 2.55
    b += brightness.value * 2.55
    
    // 对比度调整
    const factor = (259 * (contrast.value + 255)) / (255 * (259 - contrast.value))
    r = factor * (r - 128) + 128
    g = factor * (g - 128) + 128
    b = factor * (b - 128) + 128
    
    // 饱和度调整
    const gray = 0.299 * r + 0.587 * g + 0.114 * b
    r = gray + (r - gray) * (saturation.value / 100)
    g = gray + (g - gray) * (saturation.value / 100)
    b = gray + (b - gray) * (saturation.value / 100)
    
    // 伽马校正
    r = 255 * Math.pow(r / 255, 1 / gamma.value)
    g = 255 * Math.pow(g / 255, 1 / gamma.value)
    b = 255 * Math.pow(b / 255, 1 / gamma.value)
    
    data[i] = Math.max(0, Math.min(255, r))
    data[i + 1] = Math.max(0, Math.min(255, g))
    data[i + 2] = Math.max(0, Math.min(255, b))
  }
  
  ctx.putImageData(imageData, 0, 0)
}

// 重置控制参数
const resetControls = () => {
  brightness.value = 0
  contrast.value = 100
  saturation.value = 100
  gamma.value = 1.0
  processImage()
}

// 增强水印提取
const performEnhancedExtraction = async () => {
  if (!processedCanvas.value) {
    ElMessage.warning('请先上传并处理图像')
    return
  }

  isExtracting.value = true
  
  try {
    const canvas = processedCanvas.value
    const dataUrl = canvas.toDataURL('image/png')
    
    const img = new Image()
    img.onload = async () => {
      try {
        const result = await enhancedExtractor.extractFromImage(img)
        enhancedResult.value = result
        
        if (result.success) {
          ElMessage.success(`提取成功！置信度: ${(result.confidence * 100).toFixed(1)}%`)
        } else {
          ElMessage.warning(`提取失败: ${result.error}`)
        }
      } catch (error) {
        ElMessage.error('提取过程出错')
        console.error(error)
      } finally {
        isExtracting.value = false
      }
    }
    
    img.onerror = () => {
      ElMessage.error('图像加载失败')
      isExtracting.value = false
    }
    
    img.src = dataUrl
  } catch (error) {
    ElMessage.error('提取失败')
    console.error(error)
    isExtracting.value = false
  }
}

// 水印可视化
const visualizeWatermark = async () => {
  if (!originalImage.value) {
    ElMessage.warning('请先上传图像')
    return
  }

  try {
    const canvas = originalCanvas.value!
    canvas.toBlob(async (blob) => {
      if (!blob) {
        ElMessage.error('图像转换失败')
        return
      }

      const file = new File([blob], 'screenshot.png', { type: 'image/png' })
      
      const visualizedUrl = await visualizer.processImageForVisualization(file, {
        method: 'amplify',
        strength: 30,
        threshold: 2
      })
      
      // 自动下载
      const link = document.createElement('a')
      link.href = visualizedUrl
      link.download = `watermark-visualized-${Date.now()}.png`
      link.click()
      
      ElMessage.success('可视化图像已下载！请在Photoshop中打开查看')
    }, 'image/png')
  } catch (error) {
    ElMessage.error('可视化处理失败')
    console.error(error)
  }
}

// 启动水印测试
const startWatermarkTest = async () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
  }
  
  try {
    watermarkComposer = new WatermarkComposer({
      techniques: ['css'],
      globalAlpha: 0.1, // 高强度，便于观察
      dynamicRefresh: true,
      refreshInterval: 60000
    })
    
    const watermarkData = {
      user: userStore.name || '测试用户',
      timestamp: Date.now(),
      path: route.path,
      params: route.params
    }
    
    await watermarkComposer.initialize(document.body, watermarkData)
    watermarkActive.value = true
    
    ElMessage.success('测试水印已启动（高强度模式）')
  } catch (error) {
    ElMessage.error('水印启动失败')
    console.error(error)
  }
}

// 停止水印测试
const stopWatermarkTest = () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
    watermarkComposer = null
    watermarkActive.value = false
    ElMessage.info('测试水印已停止')
  }
}

// 清理资源
onUnmounted(() => {
  stopWatermarkTest()
})
</script>

<style lang="scss" scoped>
.watermark-analyzer {
  padding: 20px;
}

.upload-area {
  text-align: center;
  padding: 40px;
}

.image-processing {
  .image-section {
    text-align: center;
    
    h4 {
      margin-bottom: 12px;
      color: #303133;
    }
    
    .image-canvas {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      max-width: 400px;
      max-height: 300px;
    }
  }
  
  .controls-section {
    :deep(.el-form-item) {
      margin-bottom: 12px;
    }
    
    :deep(.el-form-item__label) {
      font-size: 12px;
      padding-bottom: 4px;
    }
  }
}
</style>
