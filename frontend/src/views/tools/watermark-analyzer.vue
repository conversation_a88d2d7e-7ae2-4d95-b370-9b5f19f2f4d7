<template>
  <div class="watermark-analyzer">
    <el-row :gutter="20">
      <!-- 左侧：图像处理工具 -->
      <el-col :span="12">
        <el-card header="图像预处理工具">
          <div v-if="!originalImage" class="upload-area">
            <el-upload
              drag
              :auto-upload="false"
              :on-change="handleImageUpload"
              :show-file-list="false"
              accept="image/*"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">上传截图进行分析</div>
            </el-upload>
          </div>

          <div v-else class="image-processing">
            <!-- 原始图像 -->
            <div class="image-section">
              <h4>原始图像</h4>
              <canvas ref="originalCanvas" style="max-width: 100%; border: 1px solid #ddd;"></canvas>
            </div>

            <!-- 处理后图像 -->
            <div class="image-section" style="margin-top: 20px;">
              <h4>处理后图像</h4>
              <canvas ref="processedCanvas" style="max-width: 100%; border: 1px solid #ddd;"></canvas>
            </div>

            <!-- 控制面板 -->
            <div class="controls" style="margin-top: 20px;">
              <el-row :gutter="16">
                <el-col :span="12">
                  <div class="control-item">
                    <label>亮度: {{ brightness }}</label>
                    <el-slider v-model="brightness" :min="-100" :max="100" @change="processImage" />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="control-item">
                    <label>对比度: {{ contrast }}</label>
                    <el-slider v-model="contrast" :min="0" :max="300" @change="processImage" />
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="16" style="margin-top: 16px;">
                <el-col :span="12">
                  <div class="control-item">
                    <label>饱和度: {{ saturation }}</label>
                    <el-slider v-model="saturation" :min="0" :max="200" @change="processImage" />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="control-item">
                    <label>伽马值: {{ gamma }}</label>
                    <el-slider v-model="gamma" :min="0.1" :max="3" :step="0.1" @change="processImage" />
                  </div>
                </el-col>
              </el-row>

              <div style="margin-top: 16px; text-align: center;">
                <el-space>
                  <el-button @click="resetControls">重置</el-button>
                  <el-button type="primary" @click="downloadProcessedImage">下载处理后图像</el-button>
                  <el-button type="success" @click="analyzeProcessedImage">分析处理后图像</el-button>
                </el-space>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：水印提取工具 -->
      <el-col :span="12">
        <WatermarkExtractor />
      </el-col>
    </el-row>

    <!-- 底部：分析结果和技巧 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="水印分析技巧">
          <el-tabs>
            <el-tab-pane label="处理技巧" name="tips">
              <div class="tips-content">
                <h4>图像处理技巧：</h4>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-card header="亮度调整" class="tip-card">
                      <p><strong>降低亮度</strong>可以让浅色的水印更加明显</p>
                      <p>建议值：-30 到 -60</p>
                      <p>适用于：白色或浅色背景的截图</p>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card header="对比度增强" class="tip-card">
                      <p><strong>增加对比度</strong>可以突出微弱的水印信号</p>
                      <p>建议值：150 到 250</p>
                      <p>适用于：水印非常微弱的情况</p>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card header="伽马校正" class="tip-card">
                      <p><strong>调整伽马值</strong>可以改变中间调的亮度</p>
                      <p>建议值：0.3 到 0.7</p>
                      <p>适用于：需要细微调整的情况</p>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>

            <el-tab-pane label="分析方法" name="methods">
              <div class="methods-content">
                <h4>水印检测方法：</h4>
                <el-timeline>
                  <el-timeline-item timestamp="步骤 1" placement="top">
                    <el-card>
                      <h4>视觉检查</h4>
                      <p>首先通过肉眼观察处理后的图像，寻找重复的图案或文本</p>
                    </el-card>
                  </el-timeline-item>
                  <el-timeline-item timestamp="步骤 2" placement="top">
                    <el-card>
                      <h4>自动提取</h4>
                      <p>使用提取工具自动分析图像，识别嵌入的水印信息</p>
                    </el-card>
                  </el-timeline-item>
                  <el-timeline-item timestamp="步骤 3" placement="top">
                    <el-card>
                      <h4>结果验证</h4>
                      <p>验证提取的信息是否合理，检查时间戳、用户名等是否正确</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-tab-pane>

            <el-tab-pane label="常见问题" name="faq">
              <div class="faq-content">
                <el-collapse>
                  <el-collapse-item title="为什么看不到水印？" name="1">
                    <p>可能的原因：</p>
                    <ul>
                      <li>水印透明度过低，需要调整图像处理参数</li>
                      <li>截图质量不够，建议使用PNG格式</li>
                      <li>图像经过了压缩，损失了水印信息</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item title="提取失败怎么办？" name="2">
                    <p>解决方案：</p>
                    <ul>
                      <li>尝试不同的图像处理参数组合</li>
                      <li>确保图像包含完整的水印区域</li>
                      <li>检查图像是否过度压缩或模糊</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item title="如何提高提取成功率？" name="3">
                    <p>建议：</p>
                    <ul>
                      <li>使用高质量的截图（PNG格式）</li>
                      <li>避免对图像进行过度编辑</li>
                      <li>尝试多种处理参数组合</li>
                      <li>确保截图包含足够的水印重复区域</li>
                    </ul>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import WatermarkExtractor from '@/components/watermark/WatermarkExtractor.vue'
import { extractWatermarkFromUrl } from '@/utils/watermark-extractor'
import { AdvancedWatermarkExtractor, type AdvancedExtractionResult } from '@/utils/advanced-watermark-extractor'

// 响应式数据
const originalCanvas = ref<HTMLCanvasElement>()
const processedCanvas = ref<HTMLCanvasElement>()
const originalImage = ref<HTMLImageElement | null>(null)

// 图像处理参数
const brightness = ref(0)
const contrast = ref(100)
const saturation = ref(100)
const gamma = ref(1.0)

// 处理图像上传
const handleImageUpload = (file: UploadFile) => {
  if (!file.raw) return

  const img = new Image()
  img.onload = () => {
    originalImage.value = img
    nextTick(() => {
      drawOriginalImage()
      processImage()
    })
  }
  img.src = URL.createObjectURL(file.raw)
}

// 绘制原始图像
const drawOriginalImage = () => {
  if (!originalCanvas.value || !originalImage.value) return

  const canvas = originalCanvas.value
  const ctx = canvas.getContext('2d')!

  // 保持原始尺寸，不进行缩放
  canvas.width = originalImage.value.width
  canvas.height = originalImage.value.height

  // 设置画布显示样式（CSS缩放用于显示）
  const maxDisplayWidth = 600
  if (originalImage.value.width > maxDisplayWidth) {
    const scale = maxDisplayWidth / originalImage.value.width
    canvas.style.width = `${originalImage.value.width * scale}px`
    canvas.style.height = `${originalImage.value.height * scale}px`
  } else {
    canvas.style.width = `${originalImage.value.width}px`
    canvas.style.height = `${originalImage.value.height}px`
  }

  // 绘制原始尺寸图像
  ctx.drawImage(originalImage.value, 0, 0)
}

// 处理图像
const processImage = () => {
  if (!processedCanvas.value || !originalImage.value || !originalCanvas.value) return

  const canvas = processedCanvas.value
  const ctx = canvas.getContext('2d')!

  // 保持与原始画布相同的尺寸
  canvas.width = originalCanvas.value.width
  canvas.height = originalCanvas.value.height

  // 设置相同的显示样式
  canvas.style.width = originalCanvas.value.style.width
  canvas.style.height = originalCanvas.value.style.height

  // 绘制原始尺寸图像
  ctx.drawImage(originalImage.value, 0, 0, canvas.width, canvas.height)
  
  // 获取图像数据
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const data = imageData.data
  
  // 应用图像处理
  for (let i = 0; i < data.length; i += 4) {
    let r = data[i]
    let g = data[i + 1]
    let b = data[i + 2]
    
    // 亮度调整
    r += brightness.value * 2.55
    g += brightness.value * 2.55
    b += brightness.value * 2.55
    
    // 对比度调整
    const contrastFactor = contrast.value / 100
    r = ((r - 128) * contrastFactor) + 128
    g = ((g - 128) * contrastFactor) + 128
    b = ((b - 128) * contrastFactor) + 128
    
    // 伽马校正
    r = Math.pow(r / 255, gamma.value) * 255
    g = Math.pow(g / 255, gamma.value) * 255
    b = Math.pow(b / 255, gamma.value) * 255
    
    // 饱和度调整
    const gray = 0.299 * r + 0.587 * g + 0.114 * b
    const satFactor = saturation.value / 100
    r = gray + (r - gray) * satFactor
    g = gray + (g - gray) * satFactor
    b = gray + (b - gray) * satFactor
    
    // 限制值范围
    data[i] = Math.max(0, Math.min(255, r))
    data[i + 1] = Math.max(0, Math.min(255, g))
    data[i + 2] = Math.max(0, Math.min(255, b))
  }
  
  // 更新画布
  ctx.putImageData(imageData, 0, 0)
}

// 重置控制参数
const resetControls = () => {
  brightness.value = 0
  contrast.value = 100
  saturation.value = 100
  gamma.value = 1.0
  processImage()
}

// 下载处理后的图像
const downloadProcessedImage = () => {
  if (!processedCanvas.value) return

  const link = document.createElement('a')
  link.download = `processed-image-${Date.now()}.png`
  link.href = processedCanvas.value.toDataURL()
  link.click()
  
  ElMessage.success('图像已下载')
}

// 分析处理后的图像
const analyzeProcessedImage = async () => {
  if (!processedCanvas.value) return

  try {
    const dataUrl = processedCanvas.value.toDataURL()
    const result = await extractWatermarkFromUrl(dataUrl)
    
    if (result.success) {
      ElMessage.success('在处理后的图像中发现水印信息！')
    } else {
      ElMessage.warning('未在处理后的图像中发现水印信息')
    }
  } catch (error) {
    ElMessage.error('分析失败')
    console.error('分析错误:', error)
  }
}
</script>

<style lang="scss" scoped>
.watermark-analyzer {
  padding: 20px;

  .upload-area {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-section {
    h4 {
      margin-bottom: 12px;
      color: #303133;
    }
  }

  .control-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;
    }
  }

  .tips-content {
    .tip-card {
      height: 100%;
      
      p {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
  }

  .methods-content {
    h4 {
      margin-bottom: 16px;
      color: #303133;
    }
  }

  .faq-content {
    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
  }
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
}
</style>
