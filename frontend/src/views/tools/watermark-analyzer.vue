<template>
  <div class="watermark-analyzer">
    <el-page-header @back="$router.go(-1)" content="水印分析工具" />
    
    <!-- 主要功能区 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card header="图像分析">
          <!-- 图像上传 -->
          <div v-if="!uploadedImage" class="upload-area">
            <el-upload
                drag
                :auto-upload="false"
                :on-change="handleImageUpload"
                :show-file-list="false"
                accept="image/*"
            >
              <el-icon class="el-icon--upload"><upload-filled/></el-icon>
              <div class="el-upload__text">上传截图进行水印分析</div>
              <template #tip>
                <div class="el-upload__tip">支持 PNG、JPG 格式，建议使用 PNG 格式</div>
              </template>
            </el-upload>
          </div>

          <!-- 图像处理 -->
          <div v-else class="image-processing">
            <div class="image-preview">
              <img :src="uploadedImage" alt="上传的图像" style="max-width: 100%; max-height: 300px;" />
            </div>
            
            <div class="processing-controls" style="margin-top: 16px;">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="亮度">
                    <el-slider v-model="brightness" :min="-100" :max="100" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="对比度">
                    <el-slider v-model="contrast" :min="0" :max="300" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <div style="text-align: center; margin-top: 16px;">
              <el-space>
                <el-button @click="resetImage">重新上传</el-button>
                <el-button type="primary" @click="analyzeImage" :loading="analyzing">
                  分析水印
                </el-button>
                <el-button type="info" @click="downloadForPS">
                  下载PS分析版
                </el-button>
              </el-space>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card header="水印测试">
          <div class="watermark-test">
            <el-space direction="vertical" style="width: 100%;">
              <div>
                <el-tag :type="watermarkActive ? 'success' : 'info'" size="large">
                  水印状态: {{ watermarkActive ? '运行中' : '已停止' }}
                </el-tag>
              </div>
              
              <el-space>
                <el-button type="primary" @click="startWatermark" :disabled="watermarkActive">
                  启动测试水印
                </el-button>
                <el-button type="danger" @click="stopWatermark" :disabled="!watermarkActive">
                  停止水印
                </el-button>
              </el-space>
              
              <div class="user-info">
                <p><strong>当前用户:</strong> {{ userStore.name || '未知用户' }}</p>
                <p><strong>当前页面:</strong> {{ route.path }}</p>
                <p><strong>会话ID:</strong> {{ sessionId }}</p>
              </div>
              
              <el-alert
                title="使用说明"
                type="info"
                :closable="false"
                show-icon
              >
                <p>1. 点击"启动测试水印"生成水印</p>
                <p>2. 截图保存页面（<strong>建议使用PNG格式</strong>）</p>
                <p>3. 上传截图进行分析</p>
                <p>4. 或下载PS版本在Photoshop中查看</p>
              </el-alert>

              <el-alert
                title="抗压缩建议"
                type="warning"
                :closable="false"
                show-icon
                style="margin-top: 12px;"
              >
                <p><strong>为了获得最佳效果：</strong></p>
                <ul style="margin: 8px 0; padding-left: 20px;">
                  <li>使用系统截图工具（Win+Shift+S）</li>
                  <li>保存为PNG格式，避免JPEG压缩</li>
                  <li>截图后立即保存，避免多次编辑</li>
                  <li>如果水印不清晰，可尝试调整亮度到-50</li>
                </ul>
              </el-alert>
            </el-space>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分析结果 -->
    <el-row v-if="analysisResult" :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="分析结果">
          <div v-if="analysisResult.success">
            <el-result icon="success" title="检测到水印信息">
              <template #sub-title>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="用户">{{ analysisResult.data.user }}</el-descriptions-item>
                  <el-descriptions-item label="时间">{{ formatTime(analysisResult.data.timestamp) }}</el-descriptions-item>
                  <el-descriptions-item label="页面路径">{{ analysisResult.data.path }}</el-descriptions-item>
                  <el-descriptions-item label="会话ID">{{ analysisResult.data.sessionId }}</el-descriptions-item>
                </el-descriptions>
              </template>
              <template #extra>
                <el-button type="primary" @click="exportResult">导出结果</el-button>
              </template>
            </el-result>
          </div>
          
          <div v-else>
            <el-result icon="warning" title="未检测到水印">
              <template #sub-title>
                <p>{{ analysisResult.error || '图像中未发现有效的水印信息' }}</p>
                <p>建议：</p>
                <ul style="text-align: left; margin-top: 8px;">
                  <li>确保截图包含完整的页面内容</li>
                  <li>使用PNG格式保存截图</li>
                  <li>尝试调整亮度和对比度</li>
                  <li>下载PS版本在Photoshop中手动查看</li>
                </ul>
              </template>
            </el-result>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Photoshop 查看指南 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="Photoshop 查看指南">
          <el-collapse>
            <el-collapse-item title="如何在 Photoshop 中查看水印（针对压缩图像优化）" name="ps-guide">
              <div style="margin-bottom: 16px;">
                <el-tag type="warning">压缩图像专用方法</el-tag>
              </div>
              <ol>
                <li><strong>极端对比度调整：</strong>图像 → 调整 → 亮度/对比度
                  <ul>
                    <li>亮度：-80 到 -100</li>
                    <li>对比度：+100</li>
                  </ul>
                </li>
                <li><strong>色阶强化：</strong>图像 → 调整 → 色阶
                  <ul>
                    <li>将左侧黑色三角拖到中间</li>
                    <li>将右侧白色三角向左拖动</li>
                    <li>中间灰色三角向左移动</li>
                  </ul>
                </li>
                <li><strong>高通滤镜增强：</strong>
                  <ul>
                    <li>复制图层</li>
                    <li>滤镜 → 其他 → 高通（半径2-5像素）</li>
                    <li>混合模式改为"强光"或"叠加"</li>
                    <li>调整图层不透明度到50-80%</li>
                  </ul>
                </li>
                <li><strong>通道分析：</strong>
                  <ul>
                    <li>分别查看红、绿、蓝通道</li>
                    <li>水印通常在红色通道最明显</li>
                    <li>可以复制通道并单独调整</li>
                  </ul>
                </li>
                <li><strong>阴影/高光调整：</strong>图像 → 调整 → 阴影/高光
                  <ul>
                    <li>阴影：80-100%</li>
                    <li>高光：0-20%</li>
                  </ul>
                </li>
              </ol>
              <div style="margin-top: 16px;">
                <el-alert
                  title="提示"
                  type="info"
                  :closable="false"
                  show-icon
                  size="small"
                >
                  如果以上方法仍看不清，说明图像压缩程度过高，建议重新截图并保存为PNG格式
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { WatermarkSystem } from '@/utils/watermark-system'

// 基础数据
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const uploadedImage = ref<string>('')
const brightness = ref(-50)    // 大幅降低亮度，对抗压缩失真
const contrast = ref(300)      // 极大增加对比度，突出微弱水印
const analyzing = ref(false)
const watermarkActive = ref(false)
const sessionId = ref<string>('')
const analysisResult = ref<any>(null)

// 水印系统
let watermarkSystem: WatermarkSystem | null = null

// 生成会话ID
const generateSessionId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
}

// 处理图像上传
const handleImageUpload = (file: UploadFile) => {
  if (!file.raw) return

  // 检测图像格式和质量
  const fileType = file.raw.type
  const fileSize = file.raw.size

  if (fileType === 'image/jpeg') {
    ElMessage.warning('检测到JPEG格式，可能存在压缩失真，建议使用PNG格式')
  }

  if (fileSize < 100 * 1024) { // 小于100KB
    ElMessage.warning('图像文件较小，可能经过高度压缩，水印提取效果可能受影响')
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    uploadedImage.value = e.target?.result as string
    ElMessage.success('图像上传成功')

    // 自动检测是否需要更强的增强参数
    if (fileType === 'image/jpeg' || fileSize < 200 * 1024) {
      brightness.value = -70  // 更强的亮度调整
      contrast.value = 400    // 更强的对比度
      ElMessage.info('已自动调整为抗压缩参数')
    }
  }
  reader.readAsDataURL(file.raw)
}

// 重置图像
const resetImage = () => {
  uploadedImage.value = ''
  analysisResult.value = null
}

// 分析图像
const analyzeImage = async () => {
  if (!uploadedImage.value) {
    ElMessage.warning('请先上传图像')
    return
  }
  
  analyzing.value = true
  
  try {
    // 这里应该调用后端API进行分析
    // 目前模拟分析结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟分析结果
    const mockResult = {
      success: Math.random() > 0.3, // 70%成功率
      data: {
        user: userStore.name || '系统管理员',
        timestamp: Date.now() - Math.random() * 3600000,
        path: route.path,
        sessionId: sessionId.value
      },
      error: Math.random() > 0.7 ? '图像质量不足，无法提取水印' : null
    }
    
    analysisResult.value = mockResult
    
    if (mockResult.success) {
      ElMessage.success('水印分析完成')
    } else {
      ElMessage.warning('未检测到有效水印')
    }
  } catch (error) {
    ElMessage.error('分析失败')
    console.error(error)
  } finally {
    analyzing.value = false
  }
}

// 下载PS分析版
const downloadForPS = () => {
  if (!uploadedImage.value) {
    ElMessage.warning('请先上传图像')
    return
  }
  
  // 创建增强版本用于PS分析
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!
  const img = new Image()
  
  img.onload = () => {
    canvas.width = img.width
    canvas.height = img.height
    ctx.drawImage(img, 0, 0)
    
    // 应用增强处理
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data
    
    // 增强对比度和亮度
    for (let i = 0; i < data.length; i += 4) {
      // 应用亮度调整
      data[i] = Math.max(0, Math.min(255, data[i] + brightness.value * 2.55))
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + brightness.value * 2.55))
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + brightness.value * 2.55))
      
      // 应用对比度调整
      const factor = (259 * (contrast.value + 255)) / (255 * (259 - contrast.value))
      data[i] = Math.max(0, Math.min(255, factor * (data[i] - 128) + 128))
      data[i + 1] = Math.max(0, Math.min(255, factor * (data[i + 1] - 128) + 128))
      data[i + 2] = Math.max(0, Math.min(255, factor * (data[i + 2] - 128) + 128))
    }
    
    ctx.putImageData(imageData, 0, 0)
    
    // 下载增强版本
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `watermark-analysis-${Date.now()}.png`
        link.click()
        URL.revokeObjectURL(url)
        ElMessage.success('PS分析版已下载')
      }
    }, 'image/png')
  }
  
  img.src = uploadedImage.value
}

// 启动水印
const startWatermark = async () => {
  if (!watermarkSystem) {
    watermarkSystem = new WatermarkSystem({
      strength: 0.15, // 大幅提高强度，确保截图后可见
      techniques: ['canvas', 'css', 'dom'],
      antiCompression: true,
      antiInterference: true,
      debugMode: true
    })
  }
  
  const success = await watermarkSystem.initialize(document.body, {
    user: userStore.name || '系统管理员',
    timestamp: Date.now(),
    path: route.path,
    sessionId: sessionId.value
  })
  
  if (success) {
    watermarkActive.value = true
    ElMessage.success('测试水印已启动')
  } else {
    ElMessage.error('水印启动失败')
  }
}

// 停止水印
const stopWatermark = () => {
  if (watermarkSystem) {
    watermarkSystem.destroy()
    watermarkActive.value = false
    ElMessage.info('测试水印已停止')
  }
}

// 导出结果
const exportResult = () => {
  if (!analysisResult.value?.success) return
  
  const result = {
    timestamp: new Date().toISOString(),
    analysis: analysisResult.value.data,
    metadata: {
      analyzer: 'WatermarkSystem v2.0',
      browser: navigator.userAgent,
      screen: `${screen.width}x${screen.height}`
    }
  }
  
  const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `watermark-result-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)
}

// 格式化时间
const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(() => {
  sessionId.value = generateSessionId()
})

onUnmounted(() => {
  stopWatermark()
})
</script>

<style lang="scss" scoped>
.watermark-analyzer {
  padding: 20px;
}

.upload-area {
  text-align: center;
  padding: 40px;
}

.image-processing {
  .image-preview {
    text-align: center;
    margin-bottom: 16px;
  }
}

.watermark-test {
  .user-info {
    background: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    
    p {
      margin: 4px 0;
    }
  }
}
</style>
