<template>
  <div class="watermark-analyzer">
    <el-row :gutter="20">
      <!-- 左侧：图像处理工具 -->
      <el-col :span="12">
        <el-card header="图像预处理工具">
          <div v-if="!originalImage" class="upload-area">
            <el-upload
              drag
              :auto-upload="false"
              :on-change="handleImageUpload"
              :show-file-list="false"
              accept="image/*"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">上传截图进行分析</div>
            </el-upload>
          </div>

          <div v-else class="image-processing">
            <!-- 原始图像 -->
            <div class="image-section">
              <h4>原始图像</h4>
              <canvas ref="originalCanvas" style="max-width: 100%; border: 1px solid #ddd;"></canvas>
            </div>

            <!-- 处理后图像 -->
            <div class="image-section" style="margin-top: 20px;">
              <h4>处理后图像</h4>
              <canvas ref="processedCanvas" style="max-width: 100%; border: 1px solid #ddd;"></canvas>
            </div>

            <!-- 控制面板 -->
            <div class="controls" style="margin-top: 20px;">
              <el-row :gutter="16">
                <el-col :span="12">
                  <div class="control-item">
                    <label>亮度: {{ brightness }}</label>
                    <el-slider v-model="brightness" :min="-100" :max="100" @change="processImage" />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="control-item">
                    <label>对比度: {{ contrast }}</label>
                    <el-slider v-model="contrast" :min="0" :max="300" @change="processImage" />
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="16" style="margin-top: 16px;">
                <el-col :span="12">
                  <div class="control-item">
                    <label>饱和度: {{ saturation }}</label>
                    <el-slider v-model="saturation" :min="0" :max="200" @change="processImage" />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="control-item">
                    <label>伽马值: {{ gamma }}</label>
                    <el-slider v-model="gamma" :min="0.1" :max="3" :step="0.1" @change="processImage" />
                  </div>
                </el-col>
              </el-row>

              <div style="margin-top: 16px; text-align: center;">
                <el-space>
                  <el-button @click="resetControls">重置参数</el-button>
                  <el-button type="warning" @click="performEnhancedExtraction" :disabled="!processedCanvas" :loading="isExtracting">
                    提取水印
                  </el-button>
                  <el-button type="info" @click="visualizeWatermark" :disabled="!originalImage">
                    可视化增强
                  </el-button>
                  <el-button type="primary" @click="downloadProcessedImage">下载图像</el-button>
                </el-space>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：水印提取工具 -->
      <el-col :span="12">
        <el-card header="水印提取工具">
          <WatermarkExtractor />
        </el-card>
      </el-col>
    </el-row>



    <!-- 增强提取结果显示 -->
    <el-row v-if="enhancedResult" :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>增强提取结果</span>
              <el-tag :type="enhancedResult.success ? 'success' : 'danger'">
                {{ enhancedResult.success ? '成功' : '失败' }}
              </el-tag>
            </div>
          </template>

          <div v-if="enhancedResult.success">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="置信度">
                <el-progress
                  :percentage="Math.round(enhancedResult.confidence * 100)"
                  :color="enhancedResult.confidence > 0.7 ? '#67c23a' : enhancedResult.confidence > 0.4 ? '#e6a23c' : '#f56c6c'"
                />
              </el-descriptions-item>
              <el-descriptions-item label="提取方法">
                {{ enhancedResult.extractionMethod }}
              </el-descriptions-item>
              <el-descriptions-item label="用户" v-if="enhancedResult.watermarkData?.user">
                {{ enhancedResult.watermarkData.user }}
              </el-descriptions-item>
              <el-descriptions-item label="时间戳" v-if="enhancedResult.watermarkData?.timestamp">
                {{ new Date(enhancedResult.watermarkData.timestamp).toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="路径" v-if="enhancedResult.watermarkData?.path">
                {{ enhancedResult.watermarkData.path }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-else>
            <el-alert
              :title="enhancedResult.error || '未知错误'"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 水印测试功能 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="水印测试与配置">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form :model="testConfig" label-width="80px" size="small">
                <el-form-item label="测试模式">
                  <el-select v-model="selectedTestMode" @change="changeTestMode" size="small" style="width: 100%;">
                    <el-option label="隐蔽模式 (0.001)" value="stealth" />
                    <el-option label="标准模式 (0.01)" value="standard" />
                    <el-option label="可视化模式 (0.1)" value="visualization" />
                  </el-select>
                </el-form-item>

                <el-form-item label="水印强度">
                  <el-slider
                    v-model="testConfig.strength"
                    :min="0.0001"
                    :max="0.2"
                    :step="0.0001"
                    :format-tooltip="formatStrength"
                    @change="updateWatermarkStrength"
                  />
                </el-form-item>

                <el-form-item label="水印技术">
                  <el-checkbox-group v-model="testConfig.techniques" @change="updateWatermarkTechniques">
                    <el-checkbox label="frequency">频域</el-checkbox>
                    <el-checkbox label="css">CSS</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </el-col>

            <el-col :span="12">
              <div style="padding: 20px; text-align: center;">
                <div style="margin-bottom: 16px;">
                  <el-tag :type="watermarkActive ? 'success' : 'info'" size="large">
                    水印状态: {{ watermarkActive ? '运行中' : '已停止' }}
                  </el-tag>
                </div>

                <el-space direction="vertical">
                  <el-button type="primary" @click="startWatermarkTest" :disabled="watermarkActive">
                    启动测试水印
                  </el-button>
                  <el-button type="danger" @click="stopWatermarkTest" :disabled="!watermarkActive">
                    停止水印
                  </el-button>
                  <el-button type="success" @click="testScreenshot" size="small">
                    测试截图
                  </el-button>
                </el-space>

                <div style="margin-top: 16px; font-size: 12px; color: #666;">
                  <p>当前用户: {{ userStore.name || '未知用户' }}</p>
                  <p>页面路径: {{ route.path }}</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 底部：分析结果和技巧 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="水印分析技巧">
          <el-tabs>
            <el-tab-pane label="处理技巧" name="tips">
              <div class="tips-content">
                <h4>图像处理技巧：</h4>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-card header="亮度调整" class="tip-card">
                      <p><strong>降低亮度</strong>可以让浅色的水印更加明显</p>
                      <p>建议值：-30 到 -60</p>
                      <p>适用于：白色或浅色背景的截图</p>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card header="对比度增强" class="tip-card">
                      <p><strong>增加对比度</strong>可以突出微弱的水印信号</p>
                      <p>建议值：150 到 250</p>
                      <p>适用于：水印非常微弱的情况</p>
                    </el-card>
                  </el-col>
                  <el-col :span="8">
                    <el-card header="伽马校正" class="tip-card">
                      <p><strong>调整伽马值</strong>可以改变中间调的亮度</p>
                      <p>建议值：0.3 到 0.7</p>
                      <p>适用于：需要细微调整的情况</p>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>

            <el-tab-pane label="分析方法" name="methods">
              <div class="methods-content">
                <h4>水印检测方法：</h4>
                <el-timeline>
                  <el-timeline-item timestamp="步骤 1" placement="top">
                    <el-card>
                      <h4>视觉检查</h4>
                      <p>首先通过肉眼观察处理后的图像，寻找重复的图案或文本</p>
                    </el-card>
                  </el-timeline-item>
                  <el-timeline-item timestamp="步骤 2" placement="top">
                    <el-card>
                      <h4>自动提取</h4>
                      <p>使用提取工具自动分析图像，识别嵌入的水印信息</p>
                    </el-card>
                  </el-timeline-item>
                  <el-timeline-item timestamp="步骤 3" placement="top">
                    <el-card>
                      <h4>结果验证</h4>
                      <p>验证提取的信息是否合理，检查时间戳、用户名等是否正确</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-tab-pane>

            <el-tab-pane label="常见问题" name="faq">
              <div class="faq-content">
                <el-collapse>
                  <el-collapse-item title="为什么看不到水印？" name="1">
                    <p>可能的原因：</p>
                    <ul>
                      <li>水印透明度过低，需要调整图像处理参数</li>
                      <li>截图质量不够，建议使用PNG格式</li>
                      <li>图像经过了压缩，损失了水印信息</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item title="提取失败怎么办？" name="2">
                    <p>解决方案：</p>
                    <ul>
                      <li>尝试不同的图像处理参数组合</li>
                      <li>确保图像包含完整的水印区域</li>
                      <li>检查图像是否过度压缩或模糊</li>
                    </ul>
                  </el-collapse-item>
                  <el-collapse-item title="如何提高提取成功率？" name="3">
                    <p>建议：</p>
                    <ul>
                      <li>使用高质量的截图（PNG格式）</li>
                      <li>先用"可视化增强"处理图像</li>
                      <li>在Photoshop中调整对比度到+100</li>
                      <li>尝试高通滤镜+叠加混合模式</li>
                    </ul>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import WatermarkExtractor from '@/components/watermark/WatermarkExtractor.vue'
import { extractWatermarkFromUrl } from '@/utils/watermark-extractor'
import { EnhancedWatermarkExtractor, type ExtractionResult } from '@/utils/enhanced-watermark-extractor'
import { WatermarkComposer } from '@/utils/watermark-composer'
import { watermarkConfigManager } from '@/utils/watermark-config'
import { WatermarkVisualizer } from '@/utils/watermark-visualizer'

// 基础数据
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const originalCanvas = ref<HTMLCanvasElement>()
const processedCanvas = ref<HTMLCanvasElement>()
const originalImage = ref<HTMLImageElement | null>(null)

// 图像处理参数
const brightness = ref(0)
const contrast = ref(100)
const saturation = ref(100)
const gamma = ref(1.0)

// 水印提取相关
const extractionResult = ref<any>(null)
const isExtracting = ref(false)
const enhancedExtractor = new EnhancedWatermarkExtractor()
const enhancedResult = ref<ExtractionResult | null>(null)

// 水印测试相关
let watermarkComposer: WatermarkComposer | null = null
const watermarkActive = ref(false)
const selectedTestMode = ref('stealth')
const testConfig = ref({
  strength: 0.001,
  techniques: ['frequency', 'css'] as any[],
  refreshInterval: 60000
})

// 水印可视化相关
const visualizer = new WatermarkVisualizer()
const visualizedImageUrl = ref<string>('')

// 处理图像上传
const handleImageUpload = (file: UploadFile) => {
  if (!file.raw) return

  const img = new Image()
  img.onload = () => {
    originalImage.value = img
    nextTick(() => {
      drawOriginalImage()
      processImage()
    })
  }
  img.src = URL.createObjectURL(file.raw)
}

// 绘制原始图像
const drawOriginalImage = () => {
  if (!originalCanvas.value || !originalImage.value) return

  const canvas = originalCanvas.value
  const ctx = canvas.getContext('2d')!

  // 保持原始尺寸，不进行缩放
  canvas.width = originalImage.value.width
  canvas.height = originalImage.value.height

  // 设置画布显示样式（CSS缩放用于显示）
  const maxDisplayWidth = 600
  if (originalImage.value.width > maxDisplayWidth) {
    const scale = maxDisplayWidth / originalImage.value.width
    canvas.style.width = `${originalImage.value.width * scale}px`
    canvas.style.height = `${originalImage.value.height * scale}px`
  } else {
    canvas.style.width = `${originalImage.value.width}px`
    canvas.style.height = `${originalImage.value.height}px`
  }

  // 绘制原始尺寸图像
  ctx.drawImage(originalImage.value, 0, 0)
}

// 处理图像
const processImage = () => {
  if (!processedCanvas.value || !originalImage.value || !originalCanvas.value) return

  const canvas = processedCanvas.value
  const ctx = canvas.getContext('2d')!

  // 保持与原始画布相同的尺寸
  canvas.width = originalCanvas.value.width
  canvas.height = originalCanvas.value.height

  // 设置相同的显示样式
  canvas.style.width = originalCanvas.value.style.width
  canvas.style.height = originalCanvas.value.style.height

  // 绘制原始尺寸图像
  ctx.drawImage(originalImage.value, 0, 0, canvas.width, canvas.height)
  
  // 获取图像数据
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const data = imageData.data
  
  // 应用图像处理
  for (let i = 0; i < data.length; i += 4) {
    let r = data[i]
    let g = data[i + 1]
    let b = data[i + 2]
    
    // 亮度调整
    r += brightness.value * 2.55
    g += brightness.value * 2.55
    b += brightness.value * 2.55
    
    // 对比度调整
    const contrastFactor = contrast.value / 100
    r = ((r - 128) * contrastFactor) + 128
    g = ((g - 128) * contrastFactor) + 128
    b = ((b - 128) * contrastFactor) + 128
    
    // 伽马校正
    r = Math.pow(r / 255, gamma.value) * 255
    g = Math.pow(g / 255, gamma.value) * 255
    b = Math.pow(b / 255, gamma.value) * 255
    
    // 饱和度调整
    const gray = 0.299 * r + 0.587 * g + 0.114 * b
    const satFactor = saturation.value / 100
    r = gray + (r - gray) * satFactor
    g = gray + (g - gray) * satFactor
    b = gray + (b - gray) * satFactor
    
    // 限制值范围
    data[i] = Math.max(0, Math.min(255, r))
    data[i + 1] = Math.max(0, Math.min(255, g))
    data[i + 2] = Math.max(0, Math.min(255, b))
  }
  
  // 更新画布
  ctx.putImageData(imageData, 0, 0)
}

// 重置控制参数
const resetControls = () => {
  brightness.value = 0
  contrast.value = 100
  saturation.value = 100
  gamma.value = 1.0
  processImage()
}

// 下载处理后的图像
const downloadProcessedImage = () => {
  if (!processedCanvas.value) return

  const link = document.createElement('a')
  link.download = `processed-image-${Date.now()}.png`
  link.href = processedCanvas.value.toDataURL()
  link.click()
  
  ElMessage.success('图像已下载')
}



// 增强水印提取
const performEnhancedExtraction = async () => {
  if (!processedCanvas.value) {
    ElMessage.warning('请先上传并处理图像')
    return
  }

  isExtracting.value = true

  try {
    // 从处理后的canvas创建图像
    const canvas = processedCanvas.value
    const dataUrl = canvas.toDataURL('image/png')

    // 创建图像对象
    const img = new Image()
    img.onload = async () => {
      try {
        const result = await enhancedExtractor.extractFromImage(img)
        enhancedResult.value = result

        if (result.success) {
          ElMessage.success(`增强提取成功！置信度: ${(result.confidence * 100).toFixed(1)}%`)
          console.log('提取结果:', result)
        } else {
          ElMessage.warning(`增强提取失败: ${result.error}`)
        }
      } catch (error) {
        ElMessage.error('增强提取过程出错')
        console.error(error)
      } finally {
        isExtracting.value = false
      }
    }

    img.onerror = () => {
      ElMessage.error('图像加载失败')
      isExtracting.value = false
    }

    img.src = dataUrl
  } catch (error) {
    ElMessage.error('提取失败')
    console.error(error)
    isExtracting.value = false
  }
}

// 格式化强度显示
const formatStrength = (value: number) => {
  return `${(value * 100).toFixed(4)}%`
}

// 启动水印测试
const startWatermarkTest = async () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
  }

  try {
    watermarkComposer = new WatermarkComposer({
      techniques: testConfig.value.techniques,
      globalAlpha: testConfig.value.strength,
      dynamicRefresh: true,
      refreshInterval: testConfig.value.refreshInterval
    })

    const watermarkData = {
      user: userStore.name || '测试用户',
      timestamp: Date.now(),
      path: route.path,
      params: route.params
    }

    await watermarkComposer.initialize(document.body, watermarkData)
    watermarkActive.value = true

    ElMessage.success('测试水印已启动')
  } catch (error) {
    ElMessage.error('水印启动失败')
    console.error(error)
  }
}

// 停止水印测试
const stopWatermarkTest = () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
    watermarkComposer = null
    watermarkActive.value = false
    ElMessage.info('测试水印已停止')
  }
}

// 更新水印强度
const updateWatermarkStrength = (value: number) => {
  testConfig.value.strength = value
  if (watermarkActive.value) {
    startWatermarkTest() // 重启水印
  }
}

// 更新水印技术
const updateWatermarkTechniques = (techniques: string[]) => {
  testConfig.value.techniques = techniques
  if (watermarkActive.value) {
    startWatermarkTest() // 重启水印
  }
}

// 切换测试模式
const changeTestMode = (mode: string) => {
  switch (mode) {
    case 'stealth':
      testConfig.value.strength = 0.001
      break
    case 'standard':
      testConfig.value.strength = 0.01
      break
    case 'visualization':
      testConfig.value.strength = 0.1
      break
  }

  if (watermarkActive.value) {
    startWatermarkTest() // 重启水印
  }

  ElMessage.info(`已切换到${mode === 'stealth' ? '隐蔽' : mode === 'standard' ? '标准' : '可视化'}模式`)
}

// 测试截图功能
const testScreenshot = async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
      ElMessage.warning('浏览器不支持屏幕截图功能')
      return
    }

    const stream = await navigator.mediaDevices.getDisplayMedia({
      video: { mediaSource: 'screen' }
    })

    ElMessage.success('截图功能正常，请检查截图中是否包含水印信息')

    // 停止录制
    stream.getTracks().forEach(track => track.stop())
  } catch (error) {
    ElMessage.error('截图测试失败')
    console.error(error)
  }
}

// 水印可视化
const visualizeWatermark = async () => {
  if (!originalImage.value) {
    ElMessage.warning('请先上传图像')
    return
  }

  try {
    // 将canvas转换为文件
    const canvas = originalCanvas.value!
    canvas.toBlob(async (blob) => {
      if (!blob) {
        ElMessage.error('图像转换失败')
        return
      }

      const file = new File([blob], 'screenshot.png', { type: 'image/png' })

      // 使用放大法进行可视化
      const visualizedUrl = await visualizer.processImageForVisualization(file, {
        method: 'amplify',
        strength: 30, // 高强度放大
        threshold: 2
      })

      visualizedImageUrl.value = visualizedUrl

      // 自动下载可视化后的图像
      const link = document.createElement('a')
      link.href = visualizedUrl
      link.download = `watermark-visualized-${Date.now()}.png`
      link.click()

      ElMessage.success('水印可视化完成！图像已下载，请在Photoshop中打开查看')

      // 显示操作指南
      showPhotoshopGuide()
    }, 'image/png')
  } catch (error) {
    ElMessage.error('可视化处理失败')
    console.error(error)
  }
}

// 显示Photoshop操作指南
const showPhotoshopGuide = () => {
  const guide = visualizer.generatePhotoshopGuide()

  // 创建一个新窗口显示指南
  const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
  if (newWindow) {
    newWindow.document.write(`
      <html>
        <head>
          <title>Photoshop水印查看指南</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            h1, h2 { color: #333; }
            ol, ul { margin-left: 20px; }
            code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
            .method { margin-bottom: 30px; padding: 15px; border-left: 4px solid #007cba; background: #f9f9f9; }
          </style>
        </head>
        <body>
          <pre style="white-space: pre-wrap;">${guide}</pre>
          <div class="method">
            <h3>💡 重要提示</h3>
            <p>1. 刚下载的图像已经过可视化增强处理，水印应该更容易观察</p>
            <p>2. 如果仍然看不到，请尝试上述多种方法组合使用</p>
            <p>3. 水印通常表现为规律性的点状、线状或几何图案</p>
            <p>4. 可以尝试放大图像到200-400%查看细节</p>
          </div>
        </body>
      </html>
    `)
    newWindow.document.close()
  }
}

// 清理资源
onUnmounted(() => {
  stopWatermarkTest()
  if (visualizedImageUrl.value) {
    URL.revokeObjectURL(visualizedImageUrl.value)
  }
})


</script>

<style lang="scss" scoped>
.watermark-analyzer {
  padding: 20px;

  .upload-area {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-section {
    h4 {
      margin-bottom: 12px;
      color: #303133;
    }
  }

  .control-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;
    }
  }

  .tips-content {
    .tip-card {
      height: 100%;
      
      p {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
  }

  .methods-content {
    h4 {
      margin-bottom: 16px;
      color: #303133;
    }
  }

  .faq-content {
    ul {
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
  }

  .help-section {
    h4 {
      color: #409eff;
      margin-bottom: 12px;
      font-size: 16px;
    }

    ul {
      padding-left: 16px;

      li {
        margin-bottom: 6px;
        line-height: 1.5;
        font-size: 14px;
      }
    }
  }
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
}
</style>
