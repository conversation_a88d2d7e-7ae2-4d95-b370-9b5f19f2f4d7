<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container" :class="{ 'is-fullscreen': showCompareArea }">
    <el-card class="table-card" v-if="showApprovalArea">
      <template #header>
        <div class="card-header">
          <span>初审项目信息/Review project</span>
        </div>
      </template>
      <ApprovalLog :logs="approvalLogs"/>

      <FormCanvas :formSchema="formSchema" render-mode="approval"/>
      <el-form
          scroll-to-error
          :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
          ref="formRef"
          :model="formRejectDto"
          :rules="rules"
          label-width="200px"
      >
        <el-form-item label="审核意见" prop="description">
          <el-input
              type="textarea"
              ref="descriptionRef"
              v-model="formRejectDto.description"
              :rows="8"
              clearable
              placeholder="请填写审核意见"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <!-- AI智能审核对比区域 -->
    <div class="ai-compare-area" v-if="showCompareArea">
      <div class="compare-left">
        <!-- 左侧Tab和PDF预览区域 -->
        <el-tabs v-model="activeFileTab" class="file-tabs">
          <el-tab-pane
              v-for="(fileInfo, index) in fileInfos"
              :key="index"
              :label="fileInfo.label"
              :name="fileInfo.code"
          >
            <!-- PDF预览区域 -->
            <!-- 替换为实际的 PdfViewer 组件 -->
            <!-- 添加一个容器来控制滚动 -->
            <div class="pdf-viewer-wrapper">
              <!-- 将 fileInfo.highlightedAreas 绑定到 PdfViewer -->
              <!-- 使用 ref 函数为每个动态生成的 PdfViewer 实例设置引用 -->
              <PdfViewer
                  v-if="fileInfo.url"
                  :src="fileInfo.url"
                  :highlightedAreas="fileInfo.highlightedAreas"
                  :ref="(el) => setPdfViewerRef(fileInfo.code, el as InstanceType<typeof PdfViewer> | null)"
              />
            </div>
            <!-- <div class="pdf-viewer-placeholder">PDF预览区域: {{ fileInfo.label }}</div> -->
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="compare-right">
        <!-- 右侧对比结果区域 -->
        <div class="compare-results">
          <!-- 遍历 compareResults -->
          <div
              v-for="fieldResult in compareResults"
              :key="fieldResult.fieldCode"
              class="result-section"
          >
            <h3>
              {{ fieldResult.fieldName }}
              {{
                fieldResult.nested
                    ? "共" + fieldResult.items?.length + "行"
                    : ""
              }}
              <template
                  v-if="!findFieldByCode(formSchema, fieldResult.fieldCode).annotations||!findFieldByCode(formSchema, fieldResult.fieldCode).annotations.approval">

                <el-button
                    v-if="!fieldResult.nested"
                    circle
                    size="small"
                    style="float: right"
                    @click="handleRejectField(fieldResult, null)"
                >
                  <el-icon>
                    <Comment/>
                  </el-icon>
                </el-button>
              </template>
              <template v-else>
                <el-button
                    v-if="!fieldResult.nested"
                    circle
                    size="small"
                    style="float: right"
                    @click="handleCancelRejectField(fieldResult, null)"
                >
                  <el-icon>
                    <Remove/>
                  </el-icon>
                </el-button>
                <el-text type="danger">
                  {{ findFieldByCode(formSchema, fieldResult.fieldCode).annotations.approval }}
                </el-text>
              </template>

            </h3>
            <div v-if="fieldResult.nested">
              <!-- 嵌套表单 (子表单) -->
              <div
                  v-for="(item, index) in fieldResult.items"
                  :key="item.rowIndex ?? index"
                  class="compare-item nested-item"
              >
                <h4>
                  行 {{ (item.rowIndex ?? index) + 1 }}

                  <template
                      v-if="!findFieldByCode(formSchema, fieldResult.fieldCode).annotations || !findFieldByCode(formSchema, fieldResult.fieldCode).annotations[item.rowIndex]||!findFieldByCode(formSchema, fieldResult.fieldCode).annotations[item.rowIndex].approval">
                    <el-button
                        v-if="fieldResult.nested"
                        circle
                        size="small"
                        style="float: right"
                        @click="
                      handleRejectField(fieldResult, item.rowIndex)
                    "
                    >
                      <el-icon>
                        <Comment/>
                      </el-icon>
                    </el-button>
                  </template>
                  <template v-else>
                    <el-button
                        v-if="fieldResult.nested"
                        circle
                        size="small"
                        style="float: right"
                        @click="handleCancelRejectField(fieldResult, item.rowIndex)"
                    >
                      <el-icon>
                        <Remove/>
                      </el-icon>
                    </el-button>
                    <el-text type="danger">
                      {{ findFieldByCode(formSchema, fieldResult.fieldCode).annotations[item.rowIndex].approval }}
                    </el-text>
                  </template>
                </h4>
                <p>用户输入: {{ item.userInput }}</p>
                <p v-if="item.match != 'not_found' && item.match != null">
                  相似度:
                  <span
                      :class="{
                      'high-similarity': item.match == 'true',
                      'low-similarity': item.match == 'false',
                    }"
                  >{{ (item.score * 100).toFixed(1) }}%</span
                  >
                </p>
                <p v-if="item.match != null">综合说明: {{ item.summary }}</p>
                <!-- <p>引用数量: {{ item.referenceCount }}</p> -->
                <div
                    v-if="item.referenceCount > 0"
                    class="references-container"
                >
                  <!-- <h5>引用详情:</h5> -->
                  <div
                      v-for="reference in item.references"
                      :key="reference.fileCode"
                      class="reference-detail"
                  >
                    <!-- <p>文件类型: {{ reference.fileCode }}</p> -->
                    <p>
                      {{ reference.referenceContent }}
                    </p>
                    <!-- 可以进一步展示 areas -->
                    <el-button
                        circle
                        size="small"
                        v-for="a in reference.areas"
                        :key="a.pageNumber + a.x0 + a.x1 + a.y0 + a.y1"
                        @click="handleHighlight(reference.fileCode!, a)"
                    >
                      <el-icon>
                        <Crop/>
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <!-- 非嵌套字段 -->
              <div
                  v-for="(item, index) in fieldResult.items"
                  :key="item.rowIndex ?? index"
                  class="compare-item"
              >
                <p>用户输入: {{ item.userInput }}</p>
                <p v-if="item.match != 'not_found' && item.match != null">
                  相似度:
                  <span
                      :class="{
                      'high-similarity': item.match == 'true',
                      'low-similarity': item.match == 'false',
                    }"
                  >{{ (item.score * 100).toFixed(1) }}%</span
                  >
                </p>
                <p v-if="item.match != null">综合说明: {{ item.summary }}</p>
                <!-- <p>引用数量: {{ item.referenceCount }}</p> -->
                <div
                    v-if="item.referenceCount > 0"
                    class="references-container"
                >
                  <!-- <h5>引用详情:</h5> -->
                  <div
                      v-for="reference in item.references"
                      :key="reference.fileCode"
                      class="reference-detail"
                  >
                    <!-- <p>文件类型: {{ reference.fileCode }}</p> -->
                    <p>
                      {{ reference.referenceContent }}
                    </p>
                    <!-- 可以进一步展示 areas -->
                    <el-button
                        circle
                        size="small"
                        @click="handleHighlight(reference.fileCode!, a)"
                        v-for="a in reference.areas"
                        :key="a.pageNumber + a.x0 + a.x1 + a.y0 + a.y1"
                    >
                      <el-icon>
                        <Crop/>
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 悬浮AI智能审核按钮 -->
  <div
      class="side-affix"
      v-if="unicomEnabled && formSchema.formData && formSchema.formData.TaskId"
  >
    <div class="affix-btn-group">
      <el-button
          class="affix-btn ai-audit-btn"
          circle
          @click="showAIDialog = true"
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-4-8c.78 0 1.41-.63 1.41-1.41S8.78 9.18 8 9.18s-1.41.63-1.41 1.41.63 1.41 1.41 1.41zm8 0c.78 0 1.41-.63 1.41-1.41s-.63-1.41-1.41-1.41-1.41.63-1.41 1.41.63 1.41 1.41 1.41zm-4 4c2.21 0 4-1.79 4-4h-8c0 2.21 1.79 4 4 4z"
          />
        </svg>
      </el-button>
      <div class="affix-btn-label">AI智能审核</div>
    </div>
  </div>

  <!-- AI智能审核提示弹窗 -->
  <el-dialog
      v-model="showAIDialog"
      width="420px"
      :show-close="false"
      align-center
  >
    <div class="ai-audit-dialog-content">
      <p>
        该服务基于DeepseekR1
        推理大模型，大模型会自动提取用户上传的相关附件与表单内容进行相似度比较。
      </p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showAIDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAIDialog"
        >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button type="info" @click="handleSummary"
    >汇总审核意见/Connect Comments
    </el-button>
    <el-button type="primary" @click="handApproval" :loading="loading"
    >审核通过/Approve
    </el-button>
    <el-button type="danger" @click="handleReject" :loading="loading"
    >审核驳回/Reject
    </el-button>
    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
  </div>

  <!-- 全屏模式下的关闭按钮 -->
  <el-button
      v-if="showCompareArea"
      class="close-compare-btn"
      circle
      :icon="ElIconClose"
      @click="handleCloseCompareArea"
  />
  <FieldAnnotationDialog
      :visible="annotationDialogVisible"
      :field="annotationField"
      :annotation="annotations ?? {}"
      @save="onSaveAnnotationField"
      @cancel="annotationDialogVisible = false"
  />
</template>

<script setup lang="ts">
import {ref, onMounted, computed, defineExpose} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";
// 导入 PdfViewer 组件
import PdfViewer from "@/components/common/PdfViewer.vue";
// 导入 Element Plus Icon
import {Close as ElIconClose, Comment, Crop, Remove} from "@element-plus/icons-vue";
import FieldAnnotationDialog from "@/components/dynamic-form/FieldAnnotationDialog.vue";
import ApprovalLog from "@/views/project/components/ApprovalLog.vue";

import {getProjectWithDiff} from "@/api/itmctr";
import {rejectProjectLevel4, approvalProjectLevel4} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";
import {getContentCompare} from "@/api/form-recognition";
import type {
  AnnotationValue,
  FormCompareFieldDto,
  FormCompareReferenceAreaDto,
} from "@/dtos/itmctr";

import {AuditProjectDto} from "@/dtos/itmctr";
import {getDownloadToken} from "@/api/files";
import {getAuthDownloadUrl} from "@/utils/files";
import type {FileDto} from "@/dtos/itmctr";
import {rejectFormInstance} from "@/api/dynamic-form-mgt";
import {FormRejectDto} from "@/dtos/dynamic-form-mgt.dto";

const unicomEnabled = import.meta.env.VITE_ENABLE_UNICOM === "true";

const router = useRouter();
const route = useRoute();

const descriptionRef = ref(null);

const annotationDialogVisible = ref(false);
const annotations = ref(null);
const annotationField = ref<any>(null);
const annotationRowIndex = ref<number>(-1);

// 表单数据
const formSchema = ref<FormDefinitionDto>({formData: null, groups: []});

const loading = ref<boolean>(false);

// 弹窗控制
const showAIDialog = ref(false);

// 对比结果数据
const compareResults = ref<FormCompareFieldDto[]>([]);

// 控制区域显示
const showApprovalArea = ref(true);
const showCompareArea = ref(false);

const formRejectDto = ref<FormRejectDto>({
  description: "",
  annotationValues: undefined,
});

const formRef = ref<FormInstance>();
const rules: FormRules = {
  description: [{required: true, message: "请填写审核意见", trigger: "blur"}],
};

const businessId = computed(() => {
  return route.params.businessId;
});

// 活动文件Tab
const activeFileTab = ref("ethic_committee_approved_file"); // 默认选中第一个Tab

// 文件信息列表，用于Tab和PDF预览
interface FileInfoItem {
  code: string;
  label: string;
  file?: FileDto | null;
  url?: string;
  highlightedAreas: FormCompareReferenceAreaDto[]; // Add highlightedAreas property
}

const fileInfos = ref<FileInfoItem[]>([
  {
    code: "ethic_committee_approved_file",
    label: "伦理委员会审批件",
    file: null,
    url: undefined,
    highlightedAreas: [], // Initialize with empty array
  },
  {
    code: "study_protocol",
    label: "研究方案",
    file: null,
    url: undefined,
    highlightedAreas: [], // Initialize with empty array
  },
  {
    code: "informed_consent_file",
    label: "知情同意书",
    file: null,
    url: undefined,
    highlightedAreas: [], // Initialize with empty array
  },
]);

// 用于存储动态创建的 PdfViewer 实例引用
const pdfViewerRefs = ref<
    Record<string, InstanceType<typeof PdfViewer> | null>
>({});

// 设置 PdfViewer 实例引用的方法
const setPdfViewerRef = (
    code: string,
    el: InstanceType<typeof PdfViewer> | null
) => {
  if (el) {
    pdfViewerRefs.value[code] = el;
  } else {
    delete pdfViewerRefs.value[code];
  }
};

// 辅助函数：根据 code 查找字段
function findFieldByCode(
    schema: FormDefinitionDto,
    code: string
): any | undefined {
  if (!schema?.groups) return undefined;
  for (const group of schema.groups) {
    if (group.fields) {
      for (const field of group.fields) {
        if (field.code === code) return field;
        // 如果是子表单，递归查找
        if (field.fields) {
          for (const subField of field.fields) {
            if (subField.code === code) return subField;
          }
        }
      }
    }
  }
  return undefined;
}

function onSaveAnnotationField(annotation: any) {
  annotationDialogVisible.value = false;

  if (
      annotationField.value.type == "subForm" ||
      annotationField.value.type == "multiSubForm"
  ) {
    if (annotationField.value.annotations == null) {
      annotationField.value.annotations = [];
    }

    annotationField.value.value.forEach((row, index) => {
      if (annotationField.value.annotations.length - 1 < index) {
        annotationField.value.annotations.splice(index, 0, {approval: null});
      }
    });

    annotationField.value.annotations[annotationRowIndex.value].approval =
        annotation.approval;
  } else {
    if (annotationField.value.annotations == null) {
      annotationField.value.annotations = {};
    }
    annotationField.value.annotations.approval = annotation.approval;
  }
  // console.log(annotationField.value.annotations);
}

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProjectWithDiff(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      // 查找对应的文件字段并获取下载地址
      const fileFields = [
        {code: "ethic_committee_approved_file", label: "伦理委员会审批件"},
        {code: "study_protocol", label: "研究方案"},
        {code: "informed_consent_file", label: "知情同意书"},
      ];

      for (const fieldInfo of fileFields) {
        const field = findFieldByCode(formSchema.value, fieldInfo.code);
        if (
            field?.value &&
            typeof field.value === "object" &&
            "fileId" in field.value
        ) {
          const fileDto = field.value as FileDto;

          // 查找对应的 fileInfos 元素并进行类型检查和赋值
          const fileInfoItem = fileInfos.value.find(
              (f) => f.code === fieldInfo.code
          );
          if (fileInfoItem) {
            fileInfoItem.file = fileDto; // 赋值

            try {
              const downloadTokenResponse = await getDownloadToken(
                  fileDto.fileId!
              );
              const downloadToken = downloadTokenResponse.data;
              const fileUrl = getAuthDownloadUrl(
                  fileDto.fileId!,
                  fileDto.fileName!,
                  downloadToken
              );

              // 在 fileInfos 中更新对应的 url
              fileInfoItem.url = fileUrl; // 赋值
            } catch (tokenError: any) {
              console.error(
                  `获取文件 ${fieldInfo.label} 下载 token 失败:`,
                  tokenError
              );
            }
          }
        }
      }

      loading.value = false;
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
  } finally {
    if (loadingInstance) loadingInstance.close();
  }
};

const handleHighlight = (
    fileCode: string,
    area: FormCompareReferenceAreaDto
) => {
  console.log("Highlighting:", fileCode, area);
  // 1. 切换到对应的文件Tab
  activeFileTab.value = fileCode;

  // 2. 设置需要高亮的区域，找到对应的 fileInfoItem 并更新其 highlightedAreas
  const fileInfoItem = fileInfos.value.find((f) => f.code === fileCode);
  if (fileInfoItem) {
    // Clear existing highlights for this file
    fileInfoItem.highlightedAreas = [];
    // Add the new highlight area
    fileInfoItem.highlightedAreas.push(area);
    console.log(
        `project-approval: Updated highlightedAreas for ${fileCode}:`,
        fileInfoItem.highlightedAreas
    );

    // 3. 跳转到指定页码并触发高亮绘制
    const pdfViewerInstance = pdfViewerRefs.value[fileCode];
    if (pdfViewerInstance && area.pageNumber) {
      // Assuming PdfViewer exposes currentPage
      pdfViewerInstance.currentPage = area.pageNumber;
      // Alternatively, if PdfViewer exposes renderPage:
      // pdfViewerInstance.renderPage(area.pageNumber);
      console.log(
          `project-approval: Attempting to navigate PdfViewer for ${fileCode} to page ${area.pageNumber}`
      );
    } else if (!pdfViewerInstance) {
      console.warn(
          `project-approval: PdfViewer instance not found for fileCode: ${fileCode}`
      );
    } else if (!area.pageNumber) {
      console.warn(
          `project-approval: Highlight area has no pageNumber for fileCode: ${fileCode}`,
          area
      );
    }
  } else {
    console.warn(
        `project-approval: Could not find fileInfoItem for fileCode: ${fileCode}`
    );
  }
};

const handleBack = () => {
  router.go(-1);
};

const handleCancelRejectField = async (fieldResult: any, rowIndex: number | any) => {
  const fieldCode = fieldResult.fieldCode;

  const fieldDefine = findFieldByCode(formSchema.value!, fieldCode);

  const annotations = fieldDefine.annotations;

  if (rowIndex != undefined && rowIndex >= 0) {
    annotations[rowIndex].approval = null;
  } else {
    annotations.approval = null;
  }
}

const handleRejectField = async (fieldResult: any, rowIndex: number | any) => {

  const fieldCode = fieldResult.fieldCode;

  annotationDialogVisible.value = true;
  annotationRowIndex.value = rowIndex;

  var fieldDefine = findFieldByCode(formSchema.value!, fieldCode);

  annotationField.value = fieldDefine;
  annotations.value =
      fieldDefine.annotations != null &&
      fieldDefine.annotations.length >= rowIndex
          ? fieldDefine.annotations[rowIndex]
          : null;
};

const handleSummary = async () => {
  let summary = "";
  let seq = 1;
  formSchema.value.groups.forEach((group) => {
    group.fields.forEach((field) => {
      var fieldName = field.labelZh + "/" + field.labelEn;

      var annotationStrings = "";

      if (field.annotations != null) {
        if (field.type == "multiSubForm") {
          var hasApprovalAnnotations = field.annotations.filter(
              (q) => q.approval
          );
          if (hasApprovalAnnotations.length > 0) {
            annotationStrings += "\n";
            field.annotations.forEach((row, idx) => {
              if (row.approval) {
                annotationStrings += `    第${idx + 1}行：${row.approval}`;
              }
            });
          }
        } else {
          if (field.annotations.approval) {
            annotationStrings = field.annotations.approval;
          }
        }
        if (annotationStrings) {
          summary += `${seq}. ${fieldName}：${annotationStrings}\n`;
          seq++;
        }
      }
    });
  });

  formRejectDto.value.description = summary;

  await nextTick(() => {
    // 1. 让 el-input 获取焦点
    descriptionRef.value?.focus?.();

    // 2. 滚动到该输入框（如果页面有滚动条）
    // 获取 el-input 的 DOM 元素
    const inputEl = descriptionRef.value?.$el || descriptionRef.value?.input;
    if (inputEl && typeof inputEl.scrollIntoView === "function") {
      inputEl.scrollIntoView({behavior: "smooth", block: "center"});
    }
  });
};

const handApproval = async () => {
  loading.value = true;
  try {
    await approvalProjectLevel4(String(businessId.value));
    ElMessage.success("审核成功");
    await router.push({path: "/project/system/pending-review-list-4"});
  } catch (error: any) {
    ElMessage.error(`审核失败: ${error.message || error}`);
    loading.value = false;
  }
};

const handleReject = async () => {
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {
    let annotations: Record<string, AnnotationValue> = {};
    formSchema.value.groups.forEach((group) => {
      group.fields.forEach((field) => {
        annotations[field.code] = field.annotations;
        field.fields.forEach((child) => {
          annotations[child.code] = child.annotations;
        });
      });
    });

    formRejectDto.value.annotationValues = annotations;

    await rejectProjectLevel4(String(businessId.value), formRejectDto.value);
    ElMessage.success("驳回成功");
    await router.push({path: "/project/system/pending-review-list-4"});
  } catch (error: any) {
    ElMessage.error(`驳回失败: ${error.message || error}`);
    loading.value = false;
  }
};

// AI智能审核对话框确认处理函数
const handleConfirmAIDialog = async () => {
  showAIDialog.value = false; // 先关闭对话框
  loading.value = true; // 显示加载状态

  try {
    // 调用 getContentCompare API
    const response = await getContentCompare(String(businessId.value));
    compareResults.value = response.data; // 存储对比结果

    // 隐藏审批区域，显示对比区域
    showApprovalArea.value = false;
    showCompareArea.value = true;

    // Clear any existing highlights when showing compare area
    fileInfos.value.forEach((item) => {
      item.highlightedAreas = [];
    });

    ElMessage.success("获取AI智能审核结果成功");
  } catch (error: any) {
    console.error("获取AI智能审核结果失败:", error);
    ElMessage.error("获取AI智能审核结果失败: " + (error.message || error));
  } finally {
    loading.value = false; // 隐藏加载状态
  }
};

// 处理关闭 AI 智能审核对比区域
const handleCloseCompareArea = () => {
  showCompareArea.value = false; // 隐藏对比区域
  showApprovalArea.value = true; // 显示审批区域
  // Clear all highlights when closing compare area
  fileInfos.value.forEach((item) => {
    item.highlightedAreas = [];
  });
  // 可能需要重置一些状态或滚动到顶部
};

// 组件挂载时加载表单
onMounted(async () => {
  await loadProject();
});

// 暴露需要给模板使用的方法和变量
defineExpose({
  showAIDialog,
  handleConfirmAIDialog,
  handleCloseCompareArea, // 暴露关闭对比区域的方法
  handleHighlight,
});

const approvalLogs = computed(() => {
  try {
    return JSON.parse(formSchema.value?.formData?.ApprovalHistory || "[]");
  } catch {
    return [];
  }
});
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}


// 悬浮操作按钮样式 (参考 project-add.vue)
.side-affix {
  position: fixed;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 200;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.affix-btn-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.affix-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
}

.affix-btn-label {
  margin-top: 2px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: rgba(0, 0, 0, 0.18);
  border-radius: 10px;
  padding: 1px 8px;
  text-align: center;
  font-weight: 400;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  letter-spacing: 0;
  line-height: 1.2;
}

.ai-audit-btn {
  background: #409eff; /* 改为蓝色 */
  color: #fff;
  box-shadow: 0 0 12px 2px rgba(64, 158, 255, 0.5), 0 1px 4px rgba(0, 0, 0, 0.1); /* 调整阴影颜色 */
  transition: box-shadow 0.2s;
}

.ai-audit-btn:hover {
  box-shadow: 0 0 24px 6px rgba(64, 158, 255, 0.8),
  0 2px 8px rgba(0, 0, 0, 0.15); /* 调整阴影颜色 */
}

.ai-audit-dialog-content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 12px 18px 0 18px;
  text-align: left;
}

.ai-audit-dialog-content p {
  margin-bottom: 10px;
}

.el-dialog {
  border-radius: 14px !important;
}

.affix-btn svg {
  display: block; /* 确保 SVG 是一个块级元素 */
  width: 100%; /* SVG 宽度填充容器 */
  height: 100%; /* SVG 高度填充容器 */
  padding: 8px; /* 添加一些内边距，让图标小一点，不顶满 */
  box-sizing: border-box; /* 让内边距包含在元素的总尺寸内 */
}

.app-container.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2100; /* 保留提高的 z-index 以覆盖头部 */
  /* 允许内部元素flex grow */
  display: flex;
  flex-direction: column;
  background-color: #fff; /* 保留白色背景 */
}

.ai-compare-area {
  display: flex; /* 设置为 Flex 容器 */
  gap: 20px;
  margin-top: 20px; /* 添加一些顶部外边距 */
  flex-grow: 1; /* 弹性填充剩余垂直空间 */
  min-height: 0; /* 允许缩小 */
  overflow: hidden; /* 隐藏自身的滚动条，滚动由子元素控制 */
}

.compare-left {
  flex: 1; /* 弹性填充宽度 */
  min-width: 0;
  display: flex; /* 设置为 Flex 容器 */
  flex-direction: column; /* 子元素垂直布局 */
  max-width: 50%; /* 可以设置最大宽度 */
  overflow-y: hidden; /* 隐藏自身的滚动条，滚动由内部控制 */
  padding-right: 10px; /* 为滚动条留出空间 */
}

.compare-right {
  flex: 1; /* 弹性填充宽度 */
  min-width: 0;
  overflow-y: auto; /* 右侧内容区域垂直滚动 */
  padding: 10px; /* 保持内边距 */
  //background-color: #f8f8f8; /* 添加一个浅灰色背景，与左侧区分 */
  font-size: 14px;
}

.pdf-viewer-wrapper {
  /* 移除 flex-grow，设置明确高度 */
  /* flex-grow: 1; */
  min-height: 0; /* 保留 min-height */
  height: 100%; /* 填充父容器高度 */
  /* 滚动条现在由 PdfViewer 内部控制 */
  overflow-y: visible; /* 或者 auto，取决于 PdfViewer 内部容器的设置 */
  padding: 10px 0;
  /* 添加一个背景色以便观察其区域 */
  /* background-color: rgba(0, 255, 0, 0.1); */
}

.compare-results {
  /* 右侧结果内容的容器样式 */
  display: flex; /* 设置为 Flex 容器 */
  flex-direction: column; /* 子元素垂直排列 */
  gap: 20px; /* 结果部分之间的间距 */
  padding-bottom: 50px; /* 为底部浮动按钮留出空间 */
}

.result-section {
  background-color: #fff; /* 每个结果部分的背景色 */
  border: 1px solid #eee; /* 边框 */
  border-radius: 8px; /* 圆角 */
  padding: 15px; /* 内边距 */
  //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 轻微阴影 */

  h3 {
    margin-top: 0; /* 移除顶部外边距 */
    margin-bottom: 10px; /* 底部外边距 */
    color: #333; /* 标题颜色 */
    font-size: 18px; /* 字体大小 */
    border-bottom: 1px solid #eee; /* 底部边框 */
    padding-bottom: 8px; /* 底部内边距 */
  }
}

.compare-item {
  margin-bottom: 15px; /* 每个对比项之间的间距 */
  padding-bottom: 15px; /* 底部内边距 */
  border-bottom: 1px dashed #ddd; /* 虚线分隔 */

  &:last-child {
    margin-bottom: 0; /* 最后一个项移除底部外边距 */
    padding-bottom: 0; /* 最后一个项移除底部内边距 */
    border-bottom: none; /* 最后一个项移除底部边框 */
  }

  h4 {
    margin-top: 0; /* 移除顶部外边距 */
    margin-bottom: 8px; /* 底部外边距 */
    color: #555; /* 标题颜色 */
    font-size: 16px; /* 字体大小 */
  }

  p {
    margin-bottom: 5px; /* 段落之间的间距 */
    line-height: 1.6; /* 行高 */
    color: #666; /* 文本颜色 */
  }
}

.nested-item {
  margin-left: 20px; /* 嵌套项左侧缩进 */
  border-left: 3px solid #007bff; /* 左侧强调边框 */
  padding-left: 15px; /* 左侧内边距 */
}

/* 相似度高亮样式 */
.high-similarity {
  font-weight: bold; /* 字体加粗 */
  color: #008000; /* 绿色文本 */
  background-color: #e9f7ef; /* 浅绿色背景 */
  padding: 2px 5px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
}

.low-similarity {
  font-weight: bold; /* 字体加粗 */
  color: #dc3545; /* 红色文本 */
  background-color: #f8d7da; /* 浅红色背景 */
  padding: 2px 5px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
}

.references-container {
  margin-top: 15px; /* 引用容器顶部外边距 */
  border-top: 1px dashed #ddd; /* 顶部虚线分隔 */
  padding-top: 15px; /* 顶部内边距 */

  h5 {
    margin-top: 0; /* 移除顶部外边距 */
    margin-bottom: 8px; /* 底部外边距 */
    color: #777; /* 标题颜色 */
    font-size: 14px; /* 字体大小 */
  }
}

.reference-detail {
  margin-bottom: 10px; /* 每个引用详情之间的间距 */
  //background-color: #f2f2f2; /* 引用详情背景色 */
  padding: 10px; /* 内边距 */
  border-radius: 4px; /* 圆角 */
  border: 1px solid #eee; /* 边框 */

  p {
    margin-bottom: 3px; /* 引用详情段落间距 */
    line-height: 1.5; /* 行高 */
    font-size: 14px; /* 字体大小 */
    color: #555; /* 文本颜色 */
    margin-top: 0px;
  }
}

.close-compare-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 99999; /* 确保在全屏内容和浮动按钮上方 */
  background-color: rgba(255, 255, 255, 0.8); /* 半透明白色背景 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
