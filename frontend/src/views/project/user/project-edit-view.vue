<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <div class="app-container">

      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="table-card">
            <template #header>
              <div class="card-header">
                <span>申请修改审核/Review edit application</span>
              </div>
            </template>
            <template #default>

              <el-row>
                <el-col :span="labelWidth">
                  <div>注册号<br/>Registration number</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ registrationNumber }}</el-text>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="labelWidth">
                  <div>注册题目<br/>Public title</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ publicTitle }}</el-text>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>申请时间<br/>Application time</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ applyTime }}</el-text>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>支持试验修改的相应试验方案<br/>Study protocol to support the study modification
                  </div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <FileDownloader v-if="studyProtocol.length > 0" :file="studyProtocol[0]" :auth="true"/>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>支持试验修改的相应伦理批件<br/>Ethical batch to support the study modification
                  </div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <FileDownloader v-if="ethic.length > 0" :file="ethic[0]" :auth="true"/>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="labelWidth">
                  <div>修改原因与计划修改内容<br/>Reason for modification and planned content of modification</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <pre><el-text>{{ description }}</el-text></pre>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="labelWidth">
                  <div>审核状态/Review status</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ statusString }}</el-text>
                </el-col>
              </el-row>

              <el-row v-if="comments">
                <el-col :span="labelWidth">
                  <div>审核意见/Review comments</div>
                </el-col>
                <el-col :span="24-labelWidth">
                  <el-text>{{ comments }}</el-text>
                </el-col>
              </el-row>
            </template>
          </el-card>
        </el-col>
      </el-row>
      <div class="floating-actions">

        <el-button @click="router.go(-1)">返回/Back</el-button>
      </div>

    </div>

  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  computed,
} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElLoading} from "element-plus";

import {getProject} from "@/api/itmctr";
import {formatTimestampToLocalString} from "@/utils/date";


const router = useRouter();
const route = useRoute();
const businessId = computed(() => {
  return route.params.businessId;
});
const labelWidth = 8;
const registrationNumber = ref<string>("");
const publicTitle = ref<string>("");

const studyProtocol = ref<any[]>([]);
const ethic = ref<any[]>([]);
const description = ref<string>("");
const applyTime = ref<string>("");

const statusString = ref<string>("");
const comments = ref<string>("");

const loading = ref<boolean>(false);


const loadProject = async () => {
      let loadingInstance: any;
      try {
        loading.value = true;
        loadingInstance = ElLoading.service({
          lock: true,
          text: "正在加载...",
          background: "rgba(0, 0, 0, 0.3)",
        });
        // 获取表单定义，formCode固定为test
        const response = await getProject(String(businessId.value));

        const status = response.data.formData["EditProcessStatus"];

        registrationNumber.value = response.data.formData["RegistrationNumber"];
        publicTitle.value = response.data.formData["PublicTitle"];
        studyProtocol.value = [JSON.parse(response.data.formData["StudyProtocol"])];
        ethic.value = [JSON.parse(response.data.formData["Ethic"])];
        description.value = response.data.formData["EditDescription"];
        applyTime.value = formatTimestampToLocalString(response.data.formData["EditApplyTime"]);
        comments.value = response.data.formData["EditRejectReason"];
        statusString.value = status == "Confirmed" ? "审核通过" : status == "Rejected" ? "审核未通过" : "审核中";

      } catch (error: any) {
      } finally {
        if (loadingInstance) loadingInstance.close();
      }
    }
;

onMounted(loadProject);
</script>

<style lang="scss" scoped>

.error-message {
  color: #F56C6C;
  font-size: 12px;
  margin-top: 5px;
}

// 为错误字段添加红色边框效果（可选）
.field-error {
  .el-input__wrapper,
  .el-textarea__wrapper {
    box-shadow: 0 0 0 1px #F56C6C inset !important;
  }
}


// 添加到你的样式部分
.table-card {
  .el-row {
    margin-bottom: 10px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .el-col:first-child {
      //font-weight: 200;
      //color: #606266;
      text-align: right;
      padding-right: 20px;
      font-size: 12px;
      color: #333;
    }
  }

  // 表单样式
  .el-text {
    font-size: 14px;
    line-height: 1.6;
    display: block;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    min-height: 40px;
    width: 500px;
  }

  // 修改按钮行样式
  .el-row:last-child {
    margin-top: 30px;
    border-bottom: none;

  }
}

// 响应式调整
@media (max-width: 768px) {
  .table-card .el-row .el-col {
    margin-bottom: 10px;
  }
}
</style>
