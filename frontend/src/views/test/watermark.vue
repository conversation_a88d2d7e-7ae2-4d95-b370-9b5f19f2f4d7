<template>
  <div class="watermark-test-page">
    <el-card header="暗水印系统测试">
      <div class="test-content">
        <h2>暗水印技术测试页面</h2>
        <p>这是一个用于测试新暗水印系统的页面。</p>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card header="系统状态">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="水印状态">
                  <el-tag :type="watermark.isEnabled.value ? 'success' : 'danger'">
                    {{ watermark.isEnabled.value ? '运行中' : '已停止' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="加载状态">
                  <el-tag :type="watermark.isLoading.value ? 'warning' : 'info'">
                    {{ watermark.isLoading.value ? '加载中' : '空闲' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="错误信息" v-if="watermark.error.value">
                  <el-text type="danger">{{ watermark.error.value }}</el-text>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card header="控制面板">
              <el-space direction="vertical" style="width: 100%">
                <el-button 
                  type="primary" 
                  :loading="watermark.isLoading.value"
                  @click="watermark.start()"
                  v-if="!watermark.isEnabled.value"
                  style="width: 100%"
                >
                  启动水印系统
                </el-button>
                
                <el-button 
                  type="danger" 
                  @click="watermark.stop()"
                  v-if="watermark.isEnabled.value"
                  style="width: 100%"
                >
                  停止水印系统
                </el-button>
                
                <el-button 
                  type="warning" 
                  :loading="watermark.isLoading.value"
                  @click="watermark.restart()"
                  v-if="watermark.isEnabled.value"
                  style="width: 100%"
                >
                  重启水印系统
                </el-button>
                
                <el-button 
                  type="info" 
                  :loading="watermark.isLoading.value"
                  @click="watermark.refresh()"
                  v-if="watermark.isEnabled.value"
                  style="width: 100%"
                >
                  刷新水印
                </el-button>
              </el-space>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card header="浏览器兼容性检测">
              <div class="compatibility-grid">
                <div 
                  v-for="(supported, feature) in compatibility.features" 
                  :key="feature"
                  class="compatibility-item"
                >
                  <el-tag :type="supported ? 'success' : 'danger'">
                    {{ feature }}: {{ supported ? '✓' : '✗' }}
                  </el-tag>
                </div>
              </div>
              
              <div style="margin-top: 16px">
                <h4>推荐技术:</h4>
                <el-tag 
                  v-for="tech in compatibility.recommendations" 
                  :key="tech" 
                  type="primary"
                  style="margin-right: 8px"
                >
                  {{ tech }}
                </el-tag>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card header="水印数据">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="用户">
                  {{ watermark.watermarkData.value.user }}
                </el-descriptions-item>
                <el-descriptions-item label="页面路径">
                  {{ watermark.watermarkData.value.path }}
                </el-descriptions-item>
                <el-descriptions-item label="时间戳">
                  {{ new Date(watermark.watermarkData.value.timestamp).toLocaleString() }}
                </el-descriptions-item>
                <el-descriptions-item label="路由参数">
                  {{ JSON.stringify(watermark.watermarkData.value.params) }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card header="性能统计">
              <el-descriptions :column="3" border v-if="performanceStats">
                <el-descriptions-item label="内存使用">
                  {{ performanceStats.memoryUsage?.used || 'N/A' }} MB
                </el-descriptions-item>
                <el-descriptions-item label="总内存">
                  {{ performanceStats.memoryUsage?.total || 'N/A' }} MB
                </el-descriptions-item>
                <el-descriptions-item label="图层数量">
                  {{ performanceStats.layerCount }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
        
        <!-- 测试内容区域 -->
        <div class="test-area" style="margin-top: 40px; padding: 40px; background: #f5f5f5; border-radius: 8px;">
          <h3>测试内容区域</h3>
          <p>这里是一些测试内容，用于观察水印效果。水印应该以极低的透明度覆盖在整个页面上。</p>
          
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
            <div v-for="i in 6" :key="i" style="padding: 20px; background: white; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <h4>测试卡片 {{ i }}</h4>
              <p>这是第 {{ i }} 个测试卡片的内容。水印应该覆盖在这些内容之上，但不影响正常的阅读和操作。</p>
              <el-button size="small">测试按钮</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useWatermark } from '@/composables/useWatermark'
import { detectBrowserCapabilities } from '@/utils/watermark-config'

// 使用水印系统
const watermark = useWatermark({
  techniques: ['frequency', 'css'], // 使用频域和CSS技术
  strength: 0.008, // 稍微提高强度便于测试观察
  autoStart: true, // 自动启动
  dynamicRefresh: true,
  refreshInterval: 30000 // 30秒刷新一次
})

// 浏览器兼容性检测
const compatibility = computed(() => watermark.checkCompatibility())

// 性能统计
const performanceStats = computed(() => watermark.getPerformanceStats())

onMounted(() => {
  console.log('水印测试页面已加载')
  console.log('浏览器兼容性:', compatibility.value)
})
</script>

<style lang="scss" scoped>
.watermark-test-page {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.test-content {
  h2 {
    color: #303133;
    margin-bottom: 16px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 20px;
  }
}

.compatibility-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.compatibility-item {
  .el-tag {
    width: 100%;
    justify-content: center;
  }
}

.test-area {
  h3 {
    color: #303133;
    margin-bottom: 16px;
  }
  
  h4 {
    color: #409eff;
    margin-bottom: 8px;
  }
  
  p {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 12px;
  }
}

// 确保水印不会被遮挡
:deep(.watermark-layer) {
  pointer-events: none !important;
  z-index: 9999 !important;
}
</style>
