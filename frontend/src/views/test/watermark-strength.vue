<template>
  <div class="watermark-strength-test">
    <el-card header="水印强度对比测试">
      <div class="controls">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card header="当前配置">
              <el-form label-width="100px">
                <el-form-item label="强度">
                  <el-slider v-model="currentStrength" :min="0.005" :max="0.1" :step="0.005" @change="updateWatermark" />
                  <span>{{ (currentStrength * 100).toFixed(1) }}%</span>
                </el-form-item>
                <el-form-item label="图案大小">
                  <el-slider v-model="patternSize" :min="200" :max="400" :step="50" @change="updateWatermark" />
                  <span>{{ patternSize }}px</span>
                </el-form-item>
                <el-form-item label="噪点密度">
                  <el-slider v-model="noiseDensity" :min="20" :max="100" :step="10" @change="updateWatermark" />
                  <span>{{ noiseDensity }}</span>
                </el-form-item>
                <el-form-item label="文本大小">
                  <el-slider v-model="textSize" :min="10" :max="20" :step="1" @change="updateWatermark" />
                  <span>{{ textSize }}px</span>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card header="预设配置">
              <el-space direction="vertical" style="width: 100%">
                <el-button 
                  v-for="preset in presets" 
                  :key="preset.name"
                  @click="applyPreset(preset)"
                  style="width: 100%"
                  :type="preset.name === currentPreset ? 'primary' : 'default'"
                >
                  {{ preset.name }}
                </el-button>
              </el-space>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card header="测试操作">
              <el-space direction="vertical" style="width: 100%">
                <el-button type="success" @click="captureScreen" style="width: 100%">
                  截图当前页面
                </el-button>
                <el-button type="warning" @click="generateTestImage" style="width: 100%">
                  生成测试图像
                </el-button>
                <el-button type="info" @click="showWatermarkPattern" style="width: 100%">
                  显示水印图案
                </el-button>
                <el-button type="danger" @click="resetToDefault" style="width: 100%">
                  重置为默认
                </el-button>
              </el-space>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 测试区域 -->
      <div class="test-areas" style="margin-top: 30px;">
        <el-row :gutter="20">
          <el-col :span="12" v-for="(area, index) in testAreas" :key="index">
            <el-card :header="area.title" style="margin-bottom: 20px; min-height: 300px;">
              <div :style="area.style">
                <h4>{{ area.title }}</h4>
                <p>{{ area.description }}</p>
                <div style="margin-top: 20px;">
                  <p>当前水印强度: {{ (currentStrength * 100).toFixed(1) }}%</p>
                  <p>图案尺寸: {{ patternSize }}px</p>
                  <p>噪点密度: {{ noiseDensity }}</p>
                  <p>建议截图此区域进行水印提取测试</p>
                </div>
                <el-button @click="highlightArea(index)" style="margin-top: 10px;">
                  高亮此区域
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 水印图案预览 -->
      <div v-if="showPattern" class="pattern-preview" style="margin-top: 30px;">
        <el-card header="水印图案预览">
          <div style="text-align: center;">
            <canvas ref="patternCanvas" style="border: 1px solid #ddd; max-width: 100%;"></canvas>
            <div style="margin-top: 16px;">
              <el-space>
                <el-button @click="downloadPattern">下载图案</el-button>
                <el-button @click="showPattern = false">关闭预览</el-button>
              </el-space>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 使用说明 -->
      <div style="margin-top: 30px;">
        <el-card header="测试说明">
          <el-steps :active="4" finish-status="success">
            <el-step title="调整参数" description="使用滑块调整水印强度和其他参数"></el-step>
            <el-step title="选择区域" description="在不同背景的测试区域中选择合适的截图区域"></el-step>
            <el-step title="截图测试" description="对选定区域进行截图保存"></el-step>
            <el-step title="图像处理" description="使用图像编辑软件调整亮度、对比度"></el-step>
            <el-step title="水印提取" description="使用提取工具分析处理后的图像"></el-step>
          </el-steps>
          
          <div style="margin-top: 20px;">
            <h4>最佳实践建议：</h4>
            <ul>
              <li><strong>强度设置</strong>：建议在2-5%之间，既保证可见性又不影响用户体验</li>
              <li><strong>图案大小</strong>：300px左右最佳，太小容易丢失，太大影响性能</li>
              <li><strong>噪点密度</strong>：50-70之间，提供足够的特征点用于识别</li>
              <li><strong>截图格式</strong>：使用PNG格式，避免JPEG压缩损失</li>
              <li><strong>处理技巧</strong>：降低亮度30-50%，增加对比度150-200%</li>
            </ul>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useSimpleWatermark } from '@/utils/simple-watermark'
import { useUserStore } from '@/stores/user'
import { useRoute } from 'vue-router'

// 用户和路由信息
const userStore = useUserStore()
const route = useRoute()

// 响应式数据
const currentStrength = ref(0.03)
const patternSize = ref(300)
const noiseDensity = ref(60)
const textSize = ref(16)
const currentPreset = ref('标准')
const showPattern = ref(false)
const patternCanvas = ref<HTMLCanvasElement>()

// 水印系统
const watermark = useSimpleWatermark(undefined, {
  strength: currentStrength.value,
  patternSize: patternSize.value,
  noiseDensity: noiseDensity.value,
  textSize: textSize.value,
  refreshInterval: 30000,
  enabled: true
})

// 水印数据
const watermarkData = computed(() => ({
  user: userStore.name || '测试用户',
  timestamp: Date.now(),
  path: route.fullPath,
  params: route.params
}))

// 预设配置
const presets = [
  {
    name: '微弱',
    strength: 0.01,
    patternSize: 250,
    noiseDensity: 30,
    textSize: 12
  },
  {
    name: '标准',
    strength: 0.03,
    patternSize: 300,
    noiseDensity: 60,
    textSize: 16
  },
  {
    name: '增强',
    strength: 0.05,
    patternSize: 350,
    noiseDensity: 80,
    textSize: 18
  },
  {
    name: '最强',
    strength: 0.08,
    patternSize: 400,
    noiseDensity: 100,
    textSize: 20
  }
]

// 测试区域
const testAreas = [
  {
    title: '白色背景区域',
    description: '纯白色背景，最容易观察到暗色水印',
    style: {
      background: '#ffffff',
      padding: '20px',
      minHeight: '200px',
      color: '#333'
    }
  },
  {
    title: '浅灰背景区域',
    description: '浅灰色背景，中等难度观察',
    style: {
      background: '#f5f5f5',
      padding: '20px',
      minHeight: '200px',
      color: '#333'
    }
  },
  {
    title: '渐变背景区域',
    description: '渐变背景，测试复杂背景下的水印效果',
    style: {
      background: 'linear-gradient(45deg, #e3f2fd, #bbdefb)',
      padding: '20px',
      minHeight: '200px',
      color: '#333'
    }
  },
  {
    title: '深色背景区域',
    description: '深色背景，最难观察水印的环境',
    style: {
      background: '#424242',
      padding: '20px',
      minHeight: '200px',
      color: '#fff'
    }
  }
]

// 方法
const updateWatermark = async () => {
  watermark.updateConfig({
    strength: currentStrength.value,
    patternSize: patternSize.value,
    noiseDensity: noiseDensity.value,
    textSize: textSize.value
  })
  
  await watermark.refresh(watermarkData.value)
}

const applyPreset = async (preset: any) => {
  currentStrength.value = preset.strength
  patternSize.value = preset.patternSize
  noiseDensity.value = preset.noiseDensity
  textSize.value = preset.textSize
  currentPreset.value = preset.name
  
  await updateWatermark()
  ElMessage.success(`已应用 ${preset.name} 预设`)
}

const captureScreen = () => {
  ElMessage.info('请使用系统截图工具（如 Cmd+Shift+4 或 Win+Shift+S）截图测试区域')
}

const generateTestImage = () => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!
  
  canvas.width = 800
  canvas.height = 600
  
  // 创建测试图像
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  ctx.fillStyle = '#333'
  ctx.font = '24px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('水印强度测试图像', canvas.width / 2, 100)
  
  ctx.font = '16px Arial'
  ctx.fillText(`强度: ${(currentStrength.value * 100).toFixed(1)}%`, canvas.width / 2, 150)
  ctx.fillText(`图案: ${patternSize.value}px`, canvas.width / 2, 180)
  ctx.fillText(`噪点: ${noiseDensity.value}`, canvas.width / 2, 210)
  
  // 下载
  const link = document.createElement('a')
  link.download = `watermark-strength-test-${currentStrength.value}.png`
  link.href = canvas.toDataURL('image/png')
  link.click()
  
  ElMessage.success('测试图像已生成并下载')
}

const showWatermarkPattern = () => {
  showPattern.value = true
  // 这里可以添加显示水印图案的逻辑
}

const downloadPattern = () => {
  if (patternCanvas.value) {
    const link = document.createElement('a')
    link.download = `watermark-pattern-${Date.now()}.png`
    link.href = patternCanvas.value.toDataURL('image/png')
    link.click()
  }
}

const highlightArea = (index: number) => {
  ElMessage.success(`已高亮第 ${index + 1} 个测试区域，建议截图此区域`)
}

const resetToDefault = async () => {
  await applyPreset(presets[1]) // 标准预设
}

// 生命周期
onMounted(async () => {
  await watermark.start(watermarkData.value)
})

onUnmounted(() => {
  watermark.destroy()
})
</script>

<style lang="scss" scoped>
.watermark-strength-test {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;

  .controls {
    .el-card {
      height: 100%;
    }
  }

  .test-areas {
    .el-card {
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }
    }
  }

  .pattern-preview {
    text-align: center;
  }

  ul {
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.6;
    }
  }
}
</style>
