<template>
  <div class="simple-watermark-test">
    <el-card header="简化版暗水印测试">
      <div class="test-controls">
        <el-space>
          <el-button type="primary" @click="startWatermark">启动水印</el-button>
          <el-button type="danger" @click="stopWatermark">停止水印</el-button>
          <el-button type="warning" @click="refreshWatermark">刷新水印</el-button>
          <el-button type="info" @click="checkCompatibility">检查兼容性</el-button>
        </el-space>
      </div>

      <div class="test-info" style="margin-top: 20px;">
        <el-descriptions title="水印信息" :column="2" border>
          <el-descriptions-item label="用户">{{ watermarkData.user }}</el-descriptions-item>
          <el-descriptions-item label="页面路径">{{ watermarkData.path }}</el-descriptions-item>
          <el-descriptions-item label="时间戳">{{ new Date(watermarkData.timestamp).toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="浏览器兼容性">
            <el-tag :type="isCompatible ? 'success' : 'danger'">
              {{ isCompatible ? '支持' : '不支持' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-content" style="margin-top: 40px;">
        <h3>测试内容区域</h3>
        <p>这里是测试内容，用于观察水印效果。水印应该以极低的透明度覆盖在页面上。</p>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8" v-for="i in 6" :key="i">
            <el-card :header="`测试卡片 ${i}`" style="margin-bottom: 20px;">
              <p>这是第 {{ i }} 个测试卡片。水印应该覆盖在这些内容之上，但不影响正常的阅读和操作。</p>
              <el-button size="small">测试按钮 {{ i }}</el-button>
            </el-card>
          </el-col>
        </el-row>

        <div style="height: 500px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); padding: 20px; border-radius: 8px; margin-top: 20px;">
          <h4>大面积测试区域</h4>
          <p>这是一个大面积的测试区域，用于观察水印在不同背景下的效果。</p>
          <p>水印应该在整个页面上重复显示，包括这个区域。</p>
          <p>请尝试截图这个页面，然后检查截图中是否包含水印信息。</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSimpleWatermark, checkSimpleWatermarkCompatibility } from '@/utils/simple-watermark'
import { useUserStore } from '@/stores/user'

// 用户和路由信息
const userStore = useUserStore()
const route = useRoute()

// 响应式数据
const isCompatible = ref(false)

// 水印系统
const watermark = useSimpleWatermark(undefined, {
  strength: 0.008, // 稍微提高强度便于观察
  refreshInterval: 30000, // 30秒刷新
  enabled: true
})

// 水印数据
const watermarkData = computed(() => ({
  user: userStore.name || '测试用户',
  timestamp: Date.now(),
  path: route.fullPath,
  params: route.params
}))

// 控制方法
const startWatermark = async () => {
  try {
    await watermark.start(watermarkData.value)
    ElMessage.success('水印启动成功')
  } catch (error) {
    ElMessage.error('水印启动失败')
    console.error('水印启动失败:', error)
  }
}

const stopWatermark = () => {
  try {
    watermark.stop()
    ElMessage.info('水印已停止')
  } catch (error) {
    ElMessage.error('水印停止失败')
    console.error('水印停止失败:', error)
  }
}

const refreshWatermark = async () => {
  try {
    await watermark.refresh(watermarkData.value)
    ElMessage.success('水印刷新成功')
  } catch (error) {
    ElMessage.error('水印刷新失败')
    console.error('水印刷新失败:', error)
  }
}

const checkCompatibility = () => {
  const compatible = checkSimpleWatermarkCompatibility()
  isCompatible.value = compatible
  
  if (compatible) {
    ElMessage.success('浏览器支持水印功能')
  } else {
    ElMessage.warning('浏览器不支持水印功能')
  }
}

// 生命周期
onMounted(() => {
  // 检查兼容性
  isCompatible.value = checkSimpleWatermarkCompatibility()
  
  // 自动启动水印
  if (isCompatible.value) {
    startWatermark()
  } else {
    ElMessage.warning('浏览器不支持水印功能，无法启动')
  }
})

onUnmounted(() => {
  watermark.destroy()
})
</script>

<style lang="scss" scoped>
.simple-watermark-test {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);

  .test-controls {
    text-align: center;
  }

  .test-content {
    h3 {
      color: #2d3436;
      margin-bottom: 16px;
    }

    h4 {
      color: #636e72;
      margin-bottom: 12px;
    }

    p {
      color: #636e72;
      line-height: 1.6;
      margin-bottom: 12px;
    }
  }
}

// 确保水印层级正确
:deep(.watermark-layer) {
  pointer-events: none !important;
  z-index: 9999 !important;
}
</style>
