<template>
  <div class="watermark-test">
    <el-page-header @back="$router.go(-1)" content="水印系统测试" />
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card header="水印配置">
          <el-form :model="testConfig" label-width="120px">
            <el-form-item label="配置档案">
              <el-select v-model="selectedProfile" @change="changeProfile">
                <el-option
                  v-for="(profile, key) in allProfiles"
                  :key="key"
                  :label="profile.name"
                  :value="key"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="水印强度">
              <el-slider 
                v-model="testConfig.strength" 
                :min="0.0001" 
                :max="0.01" 
                :step="0.0001"
                :format-tooltip="formatStrength"
                @change="updateStrength"
              />
            </el-form-item>
            
            <el-form-item label="启用技术">
              <el-checkbox-group v-model="testConfig.techniques" @change="updateTechniques">
                <el-checkbox label="frequency">频域水印</el-checkbox>
                <el-checkbox label="css">CSS水印</el-checkbox>
                <el-checkbox label="svg">SVG水印</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="刷新间隔">
              <el-input-number 
                v-model="testConfig.refreshInterval" 
                :min="5000" 
                :max="300000" 
                :step="5000"
                @change="updateRefreshInterval"
              />
              <span style="margin-left: 8px;">毫秒</span>
            </el-form-item>
            
            <el-form-item>
              <el-space>
                <el-button type="primary" @click="restartWatermark">重启水印</el-button>
                <el-button type="danger" @click="stopWatermark">停止水印</el-button>
                <el-button type="success" @click="testScreenshot">测试截图</el-button>
              </el-space>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card header="系统信息">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="当前用户">
              {{ userStore.name || '未知用户' }}
            </el-descriptions-item>
            <el-descriptions-item label="当前路径">
              {{ route.path }}
            </el-descriptions-item>
            <el-descriptions-item label="路由参数">
              <el-tag 
                v-for="(value, key) in route.params" 
                :key="key"
                size="small"
                style="margin: 2px;"
              >
                {{ key }}: {{ value }}
              </el-tag>
              <span v-if="Object.keys(route.params).length === 0">无</span>
            </el-descriptions-item>
            <el-descriptions-item label="水印状态">
              <el-tag :type="watermarkActive ? 'success' : 'danger'">
                {{ watermarkActive ? '运行中' : '已停止' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="浏览器兼容性">
              <div>
                <el-tag 
                  v-for="(supported, capability) in browserCapabilities" 
                  :key="capability"
                  :type="supported ? 'success' : 'warning'"
                  size="small"
                  style="margin: 2px;"
                >
                  {{ capability }}: {{ supported ? '支持' : '不支持' }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card header="性能监控" style="margin-top: 20px;">
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="内存使用">
              {{ memoryUsage }} MB
            </el-descriptions-item>
            <el-descriptions-item label="CPU核心数">
              {{ navigator.hardwareConcurrency || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备内存">
              {{ (navigator as any).deviceMemory || '未知' }} GB
            </el-descriptions-item>
            <el-descriptions-item label="像素比">
              {{ window.devicePixelRatio }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 测试内容区域 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="测试内容区域">
          <div class="test-content">
            <h2>这是一个测试页面</h2>
            <p>此页面用于测试水印系统的功能。水印应该以极低的透明度覆盖在整个页面上。</p>
            
            <el-row :gutter="20">
              <el-col :span="8" v-for="i in 6" :key="i">
                <el-card :body-style="{ padding: '20px' }">
                  <h4>测试卡片 {{ i }}</h4>
                  <p>这里是一些测试内容，用于验证水印是否正确显示在各种元素上。</p>
                  <el-button type="primary" size="small">测试按钮</el-button>
                </el-card>
              </el-col>
            </el-row>
            
            <div style="margin-top: 20px; padding: 20px; background: #f5f7fa;">
              <h3>背景区域测试</h3>
              <p>这个区域有背景色，用于测试水印在不同背景下的显示效果。</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { WatermarkComposer } from '@/utils/watermark-composer'
import { watermarkConfigManager, detectBrowserCapabilities } from '@/utils/watermark-config'

// 基础数据
const route = useRoute()
const userStore = useUserStore()

// 水印系统
let watermarkComposer: WatermarkComposer | null = null
const watermarkActive = ref(false)

// 配置数据
const allProfiles = computed(() => watermarkConfigManager.getAllProfiles())
const selectedProfile = ref('standard')

const testConfig = ref({
  strength: 0.001,
  techniques: ['frequency', 'css'] as any[],
  refreshInterval: 60000
})

// 浏览器兼容性
const browserCapabilities = ref(detectBrowserCapabilities())

// 性能监控
const memoryUsage = ref(0)

// 格式化强度显示
const formatStrength = (value: number) => {
  return `${(value * 100).toFixed(4)}%`
}

// 更新内存使用情况
const updateMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    memoryUsage.value = Math.round(memory.usedJSHeapSize / 1024 / 1024)
  }
}

// 切换配置档案
const changeProfile = (profileKey: string) => {
  watermarkConfigManager.setProfile(profileKey)
  const profile = watermarkConfigManager.getCurrentProfile()
  
  testConfig.value.strength = profile.strength
  testConfig.value.techniques = [...profile.techniques]
  testConfig.value.refreshInterval = profile.refreshInterval
  
  restartWatermark()
  ElMessage.success(`已切换到 ${profile.name} 配置`)
}

// 更新强度
const updateStrength = (value: number) => {
  watermarkConfigManager.updateProfile({ strength: value })
  restartWatermark()
}

// 更新技术
const updateTechniques = (techniques: string[]) => {
  watermarkConfigManager.updateProfile({ techniques: techniques as any })
  restartWatermark()
}

// 更新刷新间隔
const updateRefreshInterval = (interval: number) => {
  watermarkConfigManager.updateProfile({ refreshInterval: interval })
  restartWatermark()
}

// 启动水印
const startWatermark = async () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
  }
  
  try {
    const profile = watermarkConfigManager.getCurrentProfile()
    
    watermarkComposer = new WatermarkComposer({
      techniques: profile.techniques,
      globalAlpha: profile.strength,
      dynamicRefresh: true,
      refreshInterval: profile.refreshInterval
    })
    
    const watermarkData = {
      user: userStore.name || '测试用户',
      timestamp: Date.now(),
      path: route.path,
      params: route.params
    }
    
    await watermarkComposer.initialize(document.body, watermarkData)
    watermarkActive.value = true
    
    ElMessage.success('水印系统已启动')
  } catch (error) {
    ElMessage.error('水印系统启动失败')
    console.error(error)
  }
}

// 停止水印
const stopWatermark = () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
    watermarkComposer = null
    watermarkActive.value = false
    ElMessage.info('水印系统已停止')
  }
}

// 重启水印
const restartWatermark = async () => {
  stopWatermark()
  await new Promise(resolve => setTimeout(resolve, 100))
  await startWatermark()
}

// 测试截图
const testScreenshot = async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
      ElMessage.warning('浏览器不支持屏幕截图功能')
      return
    }
    
    const stream = await navigator.mediaDevices.getDisplayMedia({
      video: { mediaSource: 'screen' }
    })
    
    ElMessage.success('截图功能正常，请检查截图中是否包含水印信息')
    
    // 停止录制
    stream.getTracks().forEach(track => track.stop())
  } catch (error) {
    ElMessage.error('截图测试失败')
    console.error(error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化配置
  const profile = watermarkConfigManager.getCurrentProfile()
  selectedProfile.value = 'standard'
  testConfig.value.strength = profile.strength
  testConfig.value.techniques = [...profile.techniques]
  testConfig.value.refreshInterval = profile.refreshInterval
  
  // 启动水印
  startWatermark()
  
  // 开始性能监控
  const timer = setInterval(updateMemoryUsage, 2000)
  
  onUnmounted(() => {
    clearInterval(timer)
    stopWatermark()
  })
})
</script>

<style lang="scss" scoped>
.watermark-test {
  padding: 20px;
  min-height: 100vh;
}

.test-content {
  h2 {
    color: #303133;
    margin-bottom: 16px;
  }
  
  p {
    line-height: 1.6;
    color: #606266;
    margin-bottom: 16px;
  }
  
  h3 {
    color: #409eff;
    margin-bottom: 12px;
  }
  
  h4 {
    color: #303133;
    margin-bottom: 8px;
  }
}

:deep(.el-slider__input) {
  width: 100px;
}
</style>
