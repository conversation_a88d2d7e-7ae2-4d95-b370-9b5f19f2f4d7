<template>
  <div class="app-wrapper" ref="appWrapper">
      <div class="main-container">
        <navbar/>
        <horizontal-menu/>
        <div class="content-wrapper">
          <app-main/>
          <app-footer/>
        </div>
      </div>
  </div>



  <!-- 浮动操作按钮区域 -->
  <div class="floating-actions-wrapper" v-if="$slots.floatingActions">
    <div class="floating-actions-container">
      <slot name="floatingActions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import {AppMain, Navbar, HorizontalMenu} from "./components";
import AppFooter from "./Footer.vue";
import {useUserStore} from '@/stores/user'
import {ref, computed, onMounted, onUnmounted, watch, nextTick} from 'vue'
import {useRoute} from 'vue-router'
import { WatermarkSystem } from '@/utils/watermark-system'

// 获取当前用户名和路由信息
const userStore = useUserStore()
const username = computed(() => userStore.username || '未知用户')
const route = useRoute()
const appWrapper = ref<HTMLElement>()


// 水印系统
let watermarkSystem: WatermarkSystem | null = null

// 初始化水印系统
const initWatermark = async () => {
  if (!appWrapper.value) return

  if (!watermarkSystem) {
    watermarkSystem = new WatermarkSystem({
      strength: 0.1, // 生产环境使用适中强度
      techniques: ['canvas', 'css'],
      antiCompression: true,
      antiInterference: true,
      debugMode: import.meta.env.MODE === 'development'
    })
  }

  const success = await watermarkSystem.initialize(appWrapper.value, {
    user: username.value,
    timestamp: Date.now(),
    path: route.path,
    sessionId: generateSessionId()
  })

  if (!success) {
    console.warn('水印系统初始化失败')
  }
}

// 生成会话ID
const generateSessionId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
}

// 更新水印数据
const updateWatermark = async () => {
  if (watermarkSystem) {
    watermarkSystem.destroy()
    await initWatermark()
  }
}

// 清理水印系统
const destroyWatermark = async () => {
  if (watermarkSystem) {
    watermarkSystem.destroy()
    watermarkSystem = null
  }
}

// 设置全局用户信息供其他组件使用
;(window as any).__USER_STORE__ = userStore

// 生命周期管理
onMounted(async () => {
  // 延迟初始化，确保DOM已渲染完成
  await nextTick()
  setTimeout(initWatermark, 200)
})

onUnmounted(async () => {
  await destroyWatermark()
})

// 监听路由变化，更新水印数据
watch(() => route.fullPath, async () => {
  // 防抖处理，避免频繁更新
  await new Promise(resolve => setTimeout(resolve, 100))
  updateWatermark()
}, { flush: 'post' })

// 监听用户变化，重新初始化水印
watch(() => username.value, async (newUser, oldUser) => {
  if (newUser !== oldUser && newUser) {
    await updateWatermark()
  }
})

// 页面卸载前清理
window.addEventListener('beforeunload', async () => {
  await destroyWatermark()
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



.floating-actions-wrapper {
  position: sticky;
  bottom: 0;
  z-index: 100;

  .floating-actions-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    gap: 12px;

    // 响应式适配
    @media (max-width: 768px) {
      padding: 8px 16px;
      gap: 8px;

      :deep(.el-button) {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}


</style>
