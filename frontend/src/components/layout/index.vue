<template>
  <div class="app-wrapper">
    <div v-if="svgWatermark" style="position:fixed;left:0;top:0;width:100vw;height:100vh;z-index:99999;pointer-events:none;opacity:0.01;mix-blend-mode:multiply;">
      <img :src="svgWatermark" style="width:100vw;height:100vh;object-fit:cover;" alt="watermark" />
    </div>
    <div class="main-container">
      <navbar/>
      <horizontal-menu/>
      <div class="content-wrapper">
        <app-main/>
        <app-footer/>
      </div>
    </div>
    <!-- 浮动操作按钮区域 -->
    <div class="floating-actions-wrapper" v-if="$slots.floatingActions">
      <div class="floating-actions-container">
        <slot name="floatingActions"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {AppMain, Navbar, HorizontalMenu} from "./components";
import AppFooter from "./Footer.vue";
import {useUserStore} from '@/stores/user'
import {ref, computed, onMounted, watch} from 'vue'
import {useRoute} from 'vue-router'
// @ts-ignore
import QRCode from 'qrcode-svg'

// 获取当前用户名
const userStore = useUserStore()
const username = computed(() => userStore.name || '未知用户')

const route = useRoute()
const now = ref(new Date())

function getRouteParamsLines() {
  const params = route.params
  if (!params || Object.keys(params).length === 0) return []
  const lines = []
  for (const [k, v] of Object.entries(params)) {
    lines.push(String(k))
    lines.push(String(v))
  }
  return lines
}

const svgWatermark = ref('')

function generateSVGWatermark({ user, time, paramLines }: { user: string, time: string, paramLines: string[] }) {
  // 1. 生成二维码SVG
  const qr = new QRCode({
    content: JSON.stringify({ user, time, params: route.params }),
    width: 150,
    height: 150,
    color: '#000',
    background: 'transparent',
    ecl: 'L'
  })
  const qrSvg = qr.svg().replace('<svg', '<svg id="watermark-qr"')

  // 2. 明文SVG <text>，每行依次排下
  const fontSize = 18
  const textYStart = 170
  const textLines = [user, time, ...paramLines]
  const textSvg = textLines.map((line, i) =>
    `<text x="0" y="${textYStart + i * (fontSize + 6)}" font-size="${fontSize}" fill="#000">${line}</text>`
  ).join('')

  // 3. 组合SVG
  const unitW = 200, unitH = textYStart + textLines.length * (fontSize + 6) + 10
  let svg = `<svg width="${unitW}" height="${unitH}" xmlns="http://www.w3.org/2000/svg">${qrSvg}${textSvg}</svg>`

  // 4. 平铺SVG大画布（棋盘格分布）
  const screenW = window.innerWidth, screenH = window.innerHeight
  const COLS = Math.ceil((screenW * 2.5) / unitW) + 1
  const ROWS = Math.ceil((screenH * 2.5) / unitH)
  let bigSvg = `<svg width="${unitW * COLS}" height="${unitH * ROWS}" xmlns="http://www.w3.org/2000/svg">`
  for (let row = 0; row < ROWS; row++) {
    for (let col = 0; col < COLS; col++) {
      if ((row + col) % 2 !== 1) continue;
      const x = col * unitW
      const y = row * unitH
      bigSvg += `<g transform="translate(${x},${y})">${qrSvg}${textSvg}</g>`
    }
  }
  bigSvg += '</svg>'
  svgWatermark.value = 'data:image/svg+xml;utf8,' + encodeURIComponent(bigSvg)
}

function updateSVGWatermark() {
  const content = {
    user: username.value,
    time: new Date().toLocaleString(),
    paramLines: getRouteParamsLines()
  }
  generateSVGWatermark(content)
}

onMounted(() => {
  setInterval(() => {
    now.value = new Date()
    updateSVGWatermark()
  }, 60 * 1000)
  updateSVGWatermark()
})
watch(() => route.fullPath, updateSVGWatermark)
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.floating-actions-wrapper {
  position: sticky;
  bottom: 0;
  z-index: 100;

  .floating-actions-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    gap: 12px;

    // 响应式适配
    @media (max-width: 768px) {
      padding: 8px 16px;
      gap: 8px;

      :deep(.el-button) {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}


</style>
