<template>
  <div class="app-wrapper" ref="appWrapper">
    <!-- 新的暗水印系统会自动在这个容器中创建水印图层 -->

    <div class="main-container">
      <navbar/>
      <horizontal-menu/>
      <div class="content-wrapper">
        <app-main/>
        <app-footer/>
      </div>
    </div>
  </div>
  <!-- 浮动操作按钮区域 -->
  <div class="floating-actions-wrapper" v-if="$slots.floatingActions">
    <div class="floating-actions-container">
      <slot name="floatingActions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import {AppMain, Navbar, HorizontalMenu} from "./components";
import AppFooter from "./Footer.vue";
import {useSimpleWatermark, checkSimpleWatermarkCompatibility} from '@/utils/simple-watermark'
import {useUserStore} from '@/stores/user'
import {useRoute} from 'vue-router'
import {ref, onMounted, onUnmounted, computed} from 'vue'

// 用户和路由信息
const userStore = useUserStore()
const route = useRoute()

// 水印容器引用
const appWrapper = ref<HTMLElement>()

// 使用简化版水印系统
const watermark = useSimpleWatermark(undefined, {
  strength: 0.015, // 提高强度以增强抗压缩能力
  refreshInterval: 60000,
  enabled: true,
  patternSize: 280, // 适中的图案尺寸
  noiseDensity: 45, // 适中的噪点密度
  textSize: 14 // 适中的文本尺寸
})

// 水印数据
const watermarkData = computed(() => ({
  user: userStore.name || '未知用户',
  timestamp: Date.now(),
  path: route.fullPath,
  params: route.params
}))

// 组件挂载后启动水印
onMounted(async () => {
  // 检查浏览器兼容性
  if (!checkSimpleWatermarkCompatibility()) {
    console.warn('浏览器不支持水印功能')
    return
  }

  try {
    await watermark.start(watermarkData.value)
    console.log('暗水印系统启动成功')
  } catch (error) {
    console.error('暗水印系统启动失败:', error)
  }
})

// 组件卸载时清理水印
onUnmounted(() => {
  watermark.destroy()
})



</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.floating-actions-wrapper {
  position: sticky;
  bottom: 0;
  z-index: 100;

  .floating-actions-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    gap: 12px;

    // 响应式适配
    @media (max-width: 768px) {
      padding: 8px 16px;
      gap: 8px;

      :deep(.el-button) {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}


</style>
