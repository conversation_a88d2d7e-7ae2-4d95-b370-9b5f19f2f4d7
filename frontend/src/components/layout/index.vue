<template>
  <div class="app-wrapper" ref="appWrapper">
    <!-- 新的暗水印系统会自动在这个容器中创建水印图层 -->

    <div class="main-container">
      <navbar/>
      <horizontal-menu/>
      <div class="content-wrapper">
        <app-main/>
        <app-footer/>
      </div>
    </div>
  </div>
  <!-- 浮动操作按钮区域 -->
  <div class="floating-actions-wrapper" v-if="$slots.floatingActions">
    <div class="floating-actions-container">
      <slot name="floatingActions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import {AppMain, Navbar, HorizontalMenu} from "./components";
import AppFooter from "./Footer.vue";
import {useGlobalWatermark} from '@/composables/useWatermark'
import {ref, onMounted} from 'vue'

// 水印容器引用
const appWrapper = ref<HTMLElement>()

// 使用新的暗水印系统
const watermark = useGlobalWatermark({
  techniques: ['frequency', 'css'], // 使用频域和CSS两种技术
  strength: 0.005, // 水印强度
  autoStart: false, // 手动启动
  dynamicRefresh: true, // 启用动态刷新
  refreshInterval: 60000, // 1分钟刷新一次
  container: appWrapper // 指定容器
})

// 组件挂载后启动水印
onMounted(async () => {
  try {
    await watermark.start()
    console.log('暗水印系统启动成功')
  } catch (error) {
    console.error('暗水印系统启动失败:', error)
  }
})



</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.floating-actions-wrapper {
  position: sticky;
  bottom: 0;
  z-index: 100;

  .floating-actions-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    gap: 12px;

    // 响应式适配
    @media (max-width: 768px) {
      padding: 8px 16px;
      gap: 8px;

      :deep(.el-button) {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}


</style>
