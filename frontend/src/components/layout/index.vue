<template>
  <div class="app-wrapper" ref="appWrapper">
      <div class="main-container">
        <navbar/>
        <horizontal-menu/>
        <div class="content-wrapper">
          <app-main/>
          <app-footer/>
        </div>
      </div>
  </div>
  <!-- 浮动操作按钮区域 -->
  <div class="floating-actions-wrapper" v-if="$slots.floatingActions">
    <div class="floating-actions-container">
      <slot name="floatingActions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import {AppMain, Navbar, HorizontalMenu} from "./components";
import AppFooter from "./Footer.vue";
import {useUserStore} from '@/stores/user'
import {ref, computed, onMounted, onUnmounted, watch} from 'vue'
import {useRoute} from 'vue-router'
import { WatermarkComposer } from '@/utils/watermark-composer'
import { watermarkConfigManager } from '@/utils/watermark-config'

// 获取当前用户名和路由信息
const userStore = useUserStore()
const username = computed(() => userStore.name || '未知用户')
const route = useRoute()
const appWrapper = ref<HTMLElement>()

// 水印系统状态
let watermarkComposer: WatermarkComposer | null = null
const watermarkEnabled = ref(true)
const watermarkStatus = ref<'initializing' | 'active' | 'error' | 'disabled'>('disabled')

// 水印配置
const getWatermarkConfig = () => {
  // 根据环境和用户权限动态配置
  const isDev = import.meta.env.MODE === 'development'
  const isProduction = import.meta.env.MODE === 'production'

  if (isDev) {
    // 开发环境使用可视化配置便于调试
    return {
      techniques: ['css'] as const,
      globalAlpha: 0.02, // 稍微明显一些便于开发调试
      dynamicRefresh: true,
      refreshInterval: 30000 // 30秒刷新
    }
  } else if (isProduction) {
    // 生产环境使用隐蔽配置
    return {
      techniques: ['frequency', 'css'] as const,
      globalAlpha: 0.001, // 极低透明度
      dynamicRefresh: true,
      refreshInterval: 60000 // 1分钟刷新
    }
  } else {
    // 测试环境使用标准配置
    return {
      techniques: ['css'] as const,
      globalAlpha: 0.005,
      dynamicRefresh: true,
      refreshInterval: 45000
    }
  }
}

// 初始化水印系统
const initWatermark = async () => {
  if (!appWrapper.value || !watermarkEnabled.value) return

  try {
    watermarkStatus.value = 'initializing'

    // 清理旧的水印实例
    if (watermarkComposer) {
      watermarkComposer.destroy()
      watermarkComposer = null
    }

    // 获取配置
    const config = getWatermarkConfig()

    // 创建水印组合器
    watermarkComposer = new WatermarkComposer(config)

    // 准备水印数据
    const watermarkData = {
      user: username.value,
      timestamp: Date.now(),
      path: route.path,
      params: route.params,
      sessionId: generateSessionId(),
      userAgent: navigator.userAgent.substring(0, 50) // 截取部分UA信息
    }

    // 初始化水印
    await watermarkComposer.initialize(appWrapper.value, watermarkData)

    watermarkStatus.value = 'active'

    // 开发环境输出调试信息
    if (import.meta.env.MODE === 'development') {
      console.log('🔒 水印系统已启动', {
        user: watermarkData.user,
        path: watermarkData.path,
        strength: config.globalAlpha,
        techniques: config.techniques,
        sessionId: watermarkData.sessionId
      })
    }
  } catch (error) {
    watermarkStatus.value = 'error'
    console.warn('水印系统初始化失败:', error)

    // 生产环境下静默失败，不影响用户体验
    if (import.meta.env.MODE !== 'production') {
      console.error('水印初始化详细错误:', error)
    }
  }
}

// 生成会话ID
const generateSessionId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
}

// 更新水印数据
const updateWatermark = async () => {
  if (!watermarkComposer) return

  const watermarkData = {
    user: username.value,
    timestamp: Date.now(),
    path: route.path,
    params: route.params
  }

  await watermarkComposer.updateData(watermarkData)
}

// 清理水印系统
const destroyWatermark = () => {
  if (watermarkComposer) {
    watermarkComposer.destroy()
    watermarkComposer = null
  }
}

// 生命周期管理
onMounted(() => {
  // 延迟初始化，确保DOM已渲染
  setTimeout(initWatermark, 100)
})

onUnmounted(() => {
  destroyWatermark()
})

// 监听路由变化，更新水印数据
watch(() => route.fullPath, () => {
  updateWatermark()
}, { flush: 'post' })

// 监听用户变化，重新初始化水印
watch(() => username.value, () => {
  destroyWatermark()
  setTimeout(initWatermark, 100)
})
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.floating-actions-wrapper {
  position: sticky;
  bottom: 0;
  z-index: 100;

  .floating-actions-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    gap: 12px;

    // 响应式适配
    @media (max-width: 768px) {
      padding: 8px 16px;
      gap: 8px;

      :deep(.el-button) {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}


</style>
