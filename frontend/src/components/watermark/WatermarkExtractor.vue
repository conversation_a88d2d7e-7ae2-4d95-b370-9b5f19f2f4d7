<template>
  <div class="watermark-extractor">
    <el-card header="水印信息提取工具">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
          accept="image/*"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将截图文件拖拽到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 jpg/png/gif 等图片格式，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 图片预览 -->
      <div v-if="previewUrl" class="preview-section">
        <h4>图片预览</h4>
        <div class="image-preview">
          <img :src="previewUrl" alt="预览图片" style="max-width: 100%; max-height: 400px;" />
        </div>
        
        <div class="preview-controls">
          <el-space>
            <el-button type="primary" :loading="extracting" @click="extractWatermark">
              提取水印信息
            </el-button>
            <el-button @click="clearPreview">清除</el-button>
          </el-space>
        </div>
      </div>

      <!-- 提取结果 -->
      <div v-if="extractionResult" class="result-section">
        <h4>提取结果</h4>
        
        <el-alert
          v-if="extractionResult.success"
          title="水印信息提取成功"
          type="success"
          :closable="false"
          style="margin-bottom: 16px"
        />
        
        <el-alert
          v-else
          :title="extractionResult.error || '提取失败'"
          type="error"
          :closable="false"
          style="margin-bottom: 16px"
        />

        <!-- 成功结果显示 -->
        <div v-if="extractionResult.success && extractionResult.data">
          <el-descriptions title="水印信息" :column="2" border>
            <el-descriptions-item label="用户">
              <el-tag type="primary">{{ extractionResult.data.user }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="访问时间">
              {{ formatTimestamp(extractionResult.data.timestamp) }}
            </el-descriptions-item>
            <el-descriptions-item label="页面路径">
              <el-text type="info">{{ extractionResult.data.path }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="路由参数">
              <el-text v-if="extractionResult.data.params">
                {{ JSON.stringify(extractionResult.data.params) }}
              </el-text>
              <el-text v-else type="info">无</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="提取时间">
              {{ formatTimestamp(extractionResult.data.extractedAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="置信度">
              <el-progress 
                :percentage="Math.round(extractionResult.data.confidence * 100)" 
                :color="getConfidenceColor(extractionResult.data.confidence)"
                style="width: 200px"
              />
            </el-descriptions-item>
          </el-descriptions>

          <!-- 原始文本 -->
          <div v-if="extractionResult.rawText" style="margin-top: 16px;">
            <h5>原始提取文本</h5>
            <el-input
              v-model="extractionResult.rawText"
              type="textarea"
              :rows="3"
              readonly
              style="font-family: monospace;"
            />
          </div>

          <!-- 操作按钮 -->
          <div style="margin-top: 16px;">
            <el-space>
              <el-button type="success" @click="copyResult">复制结果</el-button>
              <el-button type="info" @click="exportResult">导出报告</el-button>
            </el-space>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="help-section">
        <el-collapse>
          <el-collapse-item title="使用说明" name="help">
            <div class="help-content">
              <h5>如何使用水印提取工具：</h5>
              <ol>
                <li><strong>截图准备</strong>：对包含水印的页面进行截图</li>
                <li><strong>图像处理</strong>：可以通过以下方式增强水印可见性：
                  <ul>
                    <li>降低图片亮度</li>
                    <li>增加对比度</li>
                    <li>调整色阶</li>
                    <li>使用图像编辑软件的"阴影/高光"功能</li>
                  </ul>
                </li>
                <li><strong>上传提取</strong>：将处理后的图片上传到此工具</li>
                <li><strong>查看结果</strong>：系统会自动分析并提取水印信息</li>
              </ol>
              
              <h5>提取原理：</h5>
              <ul>
                <li><strong>图案识别</strong>：识别重复的水印图案</li>
                <li><strong>文本识别</strong>：提取嵌入的文本信息</li>
                <li><strong>对比度增强</strong>：通过算法增强微弱的水印信号</li>
                <li><strong>频域分析</strong>：分析图像的频域特征</li>
              </ul>

              <h5>注意事项：</h5>
              <ul>
                <li>图片质量越高，提取成功率越高</li>
                <li>建议使用PNG格式保存截图</li>
                <li>避免对图片进行过度压缩</li>
                <li>如果一次提取失败，可以尝试调整图片对比度后重新提取</li>
              </ul>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { WatermarkExtractor, type ExtractionResult } from '@/utils/watermark-extractor'

// 响应式数据
const uploadRef = ref()
const previewUrl = ref('')
const extracting = ref(false)
const extractionResult = ref<ExtractionResult | null>(null)
const currentFile = ref<File | null>(null)

// 文件变化处理
const handleFileChange = (file: UploadFile) => {
  if (!file.raw) return

  // 检查文件大小
  if (file.raw.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB')
    return
  }

  // 检查文件类型
  if (!file.raw.type.startsWith('image/')) {
    ElMessage.error('请上传图片文件')
    return
  }

  currentFile.value = file.raw
  previewUrl.value = URL.createObjectURL(file.raw)
  extractionResult.value = null
}

// 提取水印
const extractWatermark = async () => {
  if (!currentFile.value) {
    ElMessage.error('请先上传图片')
    return
  }

  extracting.value = true
  
  try {
    const extractor = new WatermarkExtractor()
    const result = await extractor.extractFromFile(currentFile.value)
    extractionResult.value = result
    
    if (result.success) {
      ElMessage.success('水印信息提取成功')
    } else {
      ElMessage.warning(result.error || '未能提取到水印信息')
    }
  } catch (error) {
    ElMessage.error('提取过程中发生错误')
    console.error('水印提取错误:', error)
  } finally {
    extracting.value = false
  }
}

// 清除预览
const clearPreview = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  previewUrl.value = ''
  currentFile.value = null
  extractionResult.value = null
}

// 格式化时间戳
const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取置信度颜色
const getConfidenceColor = (confidence: number): string => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  if (confidence >= 0.4) return '#f56c6c'
  return '#909399'
}

// 复制结果
const copyResult = async () => {
  if (!extractionResult.value?.data) return

  const result = {
    用户: extractionResult.value.data.user,
    访问时间: formatTimestamp(extractionResult.value.data.timestamp),
    页面路径: extractionResult.value.data.path,
    路由参数: extractionResult.value.data.params,
    提取时间: formatTimestamp(extractionResult.value.data.extractedAt),
    置信度: `${Math.round(extractionResult.value.data.confidence * 100)}%`
  }

  try {
    await navigator.clipboard.writeText(JSON.stringify(result, null, 2))
    ElMessage.success('结果已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 导出报告
const exportResult = () => {
  if (!extractionResult.value?.data) return

  const report = {
    title: '水印提取报告',
    extractedAt: new Date().toISOString(),
    data: extractionResult.value.data,
    rawText: extractionResult.value.rawText
  }

  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const a = document.createElement('a')
  a.href = url
  a.download = `watermark-report-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  
  URL.revokeObjectURL(url)
  ElMessage.success('报告已导出')
}
</script>

<style lang="scss" scoped>
.watermark-extractor {
  .upload-section {
    margin-bottom: 24px;
  }

  .preview-section {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 16px;
      color: #303133;
    }
    
    .image-preview {
      text-align: center;
      margin-bottom: 16px;
      padding: 16px;
      border: 1px dashed #dcdfe6;
      border-radius: 6px;
      background-color: #fafafa;
    }
    
    .preview-controls {
      text-align: center;
    }
  }

  .result-section {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 16px;
      color: #303133;
    }
    
    h5 {
      margin: 16px 0 8px 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .help-section {
    .help-content {
      h5 {
        color: #409eff;
        margin: 16px 0 8px 0;
      }
      
      ol, ul {
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          line-height: 1.6;
          
          ul {
            margin-top: 8px;
          }
        }
      }
    }
  }
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>
