<template>
  <div class="watermark-manager">
    <el-card header="水印系统管理">
      <!-- 状态显示 -->
      <div class="status-section">
        <h4>系统状态</h4>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="状态" :value="watermark.isEnabled.value ? '运行中' : '已停止'" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="技术数量" :value="currentProfile.techniques.length" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="刷新间隔" :value="currentProfile.refreshInterval / 1000" suffix="秒" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="水印强度" :value="(currentProfile.strength * 100).toFixed(1)" suffix="%" />
          </el-col>
        </el-row>
      </div>

      <!-- 控制按钮 -->
      <div class="control-section">
        <h4>控制操作</h4>
        <el-space>
          <el-button 
            type="primary" 
            :loading="watermark.isLoading.value"
            @click="handleStart"
            v-if="!watermark.isEnabled.value"
          >
            启动水印
          </el-button>
          <el-button 
            type="danger" 
            @click="handleStop"
            v-if="watermark.isEnabled.value"
          >
            停止水印
          </el-button>
          <el-button 
            type="warning" 
            :loading="watermark.isLoading.value"
            @click="handleRestart"
            v-if="watermark.isEnabled.value"
          >
            重启水印
          </el-button>
          <el-button 
            type="info" 
            :loading="watermark.isLoading.value"
            @click="handleRefresh"
            v-if="watermark.isEnabled.value"
          >
            刷新水印
          </el-button>
        </el-space>
      </div>

      <!-- 配置选择 -->
      <div class="profile-section">
        <h4>配置档案</h4>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-select 
              v-model="selectedProfile" 
              placeholder="选择配置档案"
              @change="handleProfileChange"
              style="width: 100%"
            >
              <el-option
                v-for="(profile, key) in allProfiles"
                :key="key"
                :label="`${profile.name} - ${profile.description}`"
                :value="key"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-button @click="showCustomDialog = true">自定义配置</el-button>
            <el-button @click="handleReset">重置默认</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 技术选择 -->
      <div class="technique-section">
        <h4>水印技术</h4>
        <el-checkbox-group v-model="selectedTechniques" @change="handleTechniqueChange">
          <el-checkbox label="frequency">频域水印</el-checkbox>
          <el-checkbox label="css">CSS滤镜</el-checkbox>
          <el-checkbox label="svg">SVG图案</el-checkbox>
          <el-checkbox label="webgl" :disabled="!capabilities.webgl">WebGL渲染</el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- 参数调节 -->
      <div class="params-section">
        <h4>参数设置</h4>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="param-item">
              <label>水印强度: {{ (strength * 100).toFixed(1) }}%</label>
              <el-slider 
                v-model="strength" 
                :min="0.001" 
                :max="0.02" 
                :step="0.001"
                @change="handleParamChange"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="param-item">
              <label>刷新间隔: {{ refreshInterval / 1000 }}秒</label>
              <el-slider 
                v-model="refreshInterval" 
                :min="10000" 
                :max="300000" 
                :step="5000"
                @change="handleParamChange"
              />
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 兼容性信息 -->
      <div class="compatibility-section">
        <h4>浏览器兼容性</h4>
        <el-row :gutter="16">
          <el-col :span="6" v-for="(supported, feature) in capabilities" :key="feature">
            <el-tag :type="supported ? 'success' : 'danger'">
              {{ feature }}: {{ supported ? '支持' : '不支持' }}
            </el-tag>
          </el-col>
        </el-row>
      </div>

      <!-- 性能统计 -->
      <div class="performance-section" v-if="performanceStats">
        <h4>性能统计</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="内存使用">
            {{ performanceStats.memoryUsage?.used || 'N/A' }} MB
          </el-descriptions-item>
          <el-descriptions-item label="总内存">
            {{ performanceStats.memoryUsage?.total || 'N/A' }} MB
          </el-descriptions-item>
          <el-descriptions-item label="图层数量">
            {{ performanceStats.layerCount }}
          </el-descriptions-item>
          <el-descriptions-item label="错误状态">
            {{ performanceStats.hasError ? '有错误' : '正常' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 错误信息 -->
      <el-alert 
        v-if="watermark.error.value" 
        :title="watermark.error.value" 
        type="error" 
        show-icon 
        closable
      />
    </el-card>

    <!-- 自定义配置对话框 -->
    <el-dialog v-model="showCustomDialog" title="自定义水印配置" width="600px">
      <el-form :model="customConfig" label-width="120px">
        <el-form-item label="配置名称">
          <el-input v-model="customConfig.name" placeholder="输入配置名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="customConfig.description" placeholder="输入配置描述" />
        </el-form-item>
        <el-form-item label="水印技术">
          <el-checkbox-group v-model="customConfig.techniques">
            <el-checkbox label="frequency">频域水印</el-checkbox>
            <el-checkbox label="css">CSS滤镜</el-checkbox>
            <el-checkbox label="svg">SVG图案</el-checkbox>
            <el-checkbox label="webgl">WebGL渲染</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="水印强度">
          <el-slider v-model="customConfig.strength" :min="0.001" :max="0.02" :step="0.001" />
        </el-form-item>
        <el-form-item label="刷新间隔(秒)">
          <el-input-number v-model="customConfig.refreshInterval" :min="10" :max="300" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCustomDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveCustom">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useWatermark } from '@/composables/useWatermark'
import { 
  watermarkConfigManager, 
  detectBrowserCapabilities, 
  WATERMARK_PROFILES,
  type WatermarkProfile 
} from '@/utils/watermark-config'
import type { WatermarkTechnique } from '@/utils/watermark-composer'

// 水印实例
const watermark = useWatermark({
  autoStart: false
})

// 响应式数据
const selectedProfile = ref('standard')
const selectedTechniques = ref<WatermarkTechnique[]>(['frequency', 'css'])
const strength = ref(0.005)
const refreshInterval = ref(60000)
const showCustomDialog = ref(false)

// 自定义配置
const customConfig = ref({
  name: '',
  description: '',
  techniques: ['frequency', 'css'] as WatermarkTechnique[],
  strength: 0.005,
  refreshInterval: 60
})

// 计算属性
const currentProfile = computed(() => watermarkConfigManager.getCurrentProfile())
const allProfiles = computed(() => watermarkConfigManager.getAllProfiles())
const capabilities = computed(() => detectBrowserCapabilities())
const performanceStats = computed(() => watermark.getPerformanceStats())

// 控制方法
const handleStart = async () => {
  try {
    await watermark.start()
    ElMessage.success('水印系统启动成功')
  } catch (error) {
    ElMessage.error('水印系统启动失败')
  }
}

const handleStop = () => {
  watermark.stop()
  ElMessage.info('水印系统已停止')
}

const handleRestart = async () => {
  try {
    await watermark.restart()
    ElMessage.success('水印系统重启成功')
  } catch (error) {
    ElMessage.error('水印系统重启失败')
  }
}

const handleRefresh = async () => {
  try {
    await watermark.refresh()
    ElMessage.success('水印刷新成功')
  } catch (error) {
    ElMessage.error('水印刷新失败')
  }
}

const handleProfileChange = (profileName: string) => {
  if (watermarkConfigManager.setProfile(profileName)) {
    const profile = watermarkConfigManager.getCurrentProfile()
    updateLocalValues(profile)
    watermark.updateConfig({
      techniques: profile.techniques,
      strength: profile.strength,
      refreshInterval: profile.refreshInterval
    })
    ElMessage.success(`已切换到 ${profile.name} 配置`)
  }
}

const handleTechniqueChange = (techniques: WatermarkTechnique[]) => {
  watermark.updateConfig({ techniques })
  watermarkConfigManager.updateProfile({ techniques })
}

const handleParamChange = () => {
  watermark.updateConfig({
    strength,
    refreshInterval: refreshInterval.value
  })
  watermarkConfigManager.updateProfile({
    strength: strength.value,
    refreshInterval: refreshInterval.value
  })
}

const handleReset = () => {
  watermarkConfigManager.reset()
  const profile = watermarkConfigManager.getCurrentProfile()
  updateLocalValues(profile)
  watermark.updateConfig({
    techniques: profile.techniques,
    strength: profile.strength,
    refreshInterval: profile.refreshInterval
  })
  ElMessage.success('已重置为默认配置')
}

const handleSaveCustom = () => {
  const config: WatermarkProfile = {
    name: customConfig.value.name,
    description: customConfig.value.description,
    techniques: customConfig.value.techniques,
    strength: customConfig.value.strength,
    refreshInterval: customConfig.value.refreshInterval * 1000,
    enabled: true
  }
  
  if (watermarkConfigManager.addCustomProfile(customConfig.value.name, config)) {
    ElMessage.success('自定义配置保存成功')
    showCustomDialog.value = false
  } else {
    ElMessage.error('自定义配置保存失败')
  }
}

// 更新本地值
const updateLocalValues = (profile: WatermarkProfile) => {
  selectedTechniques.value = [...profile.techniques]
  strength.value = profile.strength
  refreshInterval.value = profile.refreshInterval
}

// 初始化
onMounted(() => {
  const profile = watermarkConfigManager.getCurrentProfile()
  updateLocalValues(profile)
})
</script>

<style lang="scss" scoped>
.watermark-manager {
  .status-section,
  .control-section,
  .profile-section,
  .technique-section,
  .params-section,
  .compatibility-section,
  .performance-section {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 16px;
      color: #303133;
      font-weight: 600;
    }
  }
  
  .param-item {
    label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .el-tag {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
</style>
