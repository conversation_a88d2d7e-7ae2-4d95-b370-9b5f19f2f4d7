<template>
  <div class="extraction-result-viewer">
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>水印提取结果</span>
          <div>
            <el-tag v-if="result" :type="result.success ? 'success' : 'danger'">
              {{ result.success ? '提取成功' : '提取失败' }}
            </el-tag>
            <el-button 
              v-if="result && result.success" 
              type="text" 
              size="small" 
              @click="exportResult"
            >
              导出结果
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="!result" class="no-result">
        <el-icon size="64" color="#ccc"><DocumentRemove /></el-icon>
        <p>暂无提取结果</p>
      </div>

      <div v-else-if="result.success" class="success-result">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="置信度">
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-progress 
                :percentage="Math.round(result.confidence * 100)" 
                :color="getConfidenceColor(result.confidence)"
                :stroke-width="8"
                style="flex: 1;"
              />
              <span>{{ (result.confidence * 100).toFixed(1) }}%</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="提取方法">
            <el-tag type="info">{{ result.extractionMethod || '未知' }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 水印数据 -->
        <div v-if="result.watermarkData" style="margin-top: 20px;">
          <h3>水印数据</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="用户">
              <el-tag type="primary">{{ result.watermarkData.user || '未知用户' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="时间戳">
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-icon><Clock /></el-icon>
                <span>{{ formatTimestamp(result.watermarkData.timestamp) }}</span>
                <el-tag size="small" type="info">
                  {{ getTimeAgo(result.watermarkData.timestamp) }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="访问路径">
              <el-tag type="warning">{{ result.watermarkData.path || '未知路径' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="result.watermarkData.params && Object.keys(result.watermarkData.params).length > 0" label="参数">
              <div class="params-display">
                <el-tag 
                  v-for="(value, key) in result.watermarkData.params" 
                  :key="key"
                  size="small"
                  style="margin: 2px;"
                >
                  {{ key }}: {{ value }}
                </el-tag>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 调试信息 -->
        <div v-if="result.debugInfo && showDebugInfo" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
            <h3>调试信息</h3>
            <el-button type="text" size="small" @click="showDebugInfo = false">
              隐藏
            </el-button>
          </div>
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="模式匹配数">
              {{ result.debugInfo.patternMatches }}
            </el-descriptions-item>
            <el-descriptions-item label="信号强度">
              {{ result.debugInfo.signalStrength?.toFixed(4) || 'N/A' }}
            </el-descriptions-item>
            <el-descriptions-item label="噪声水平">
              {{ result.debugInfo.noiseLevel?.toFixed(6) || 'N/A' }}
            </el-descriptions-item>
            <el-descriptions-item label="频率峰值">
              <el-tag 
                v-for="(peak, index) in result.debugInfo.frequencyPeaks?.slice(0, 3)" 
                :key="index"
                size="small"
                style="margin-right: 4px;"
              >
                {{ peak }}
              </el-tag>
              <span v-if="!result.debugInfo.frequencyPeaks?.length">无</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 显示调试信息按钮 -->
        <div v-if="result.debugInfo && !showDebugInfo" style="margin-top: 16px; text-align: center;">
          <el-button type="text" size="small" @click="showDebugInfo = true">
            显示调试信息
          </el-button>
        </div>

        <!-- 可视化图表 -->
        <div v-if="result.confidence > 0" style="margin-top: 20px;">
          <h3>置信度分析</h3>
          <div class="confidence-chart">
            <div class="confidence-bar">
              <div 
                class="confidence-fill" 
                :style="{ 
                  width: (result.confidence * 100) + '%',
                  backgroundColor: getConfidenceColor(result.confidence)
                }"
              ></div>
            </div>
            <div class="confidence-labels">
              <span>0%</span>
              <span>25%</span>
              <span>50%</span>
              <span>75%</span>
              <span>100%</span>
            </div>
            <div class="confidence-description">
              <el-tag :type="getConfidenceType(result.confidence)">
                {{ getConfidenceDescription(result.confidence) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="error-result">
        <el-alert 
          :title="result.error || '提取失败'" 
          type="error" 
          :closable="false"
          show-icon
        />
        
        <div style="margin-top: 16px;">
          <h4>可能的原因：</h4>
          <ul>
            <li>图像中不包含水印信息</li>
            <li>水印信息已被破坏或修改</li>
            <li>图像质量过低或压缩过度</li>
            <li>使用了不兼容的水印算法</li>
          </ul>
        </div>

        <div style="margin-top: 16px;">
          <h4>建议操作：</h4>
          <ul>
            <li>尝试调整图像处理参数</li>
            <li>使用原始未压缩的图像</li>
            <li>检查图像是否确实包含水印</li>
            <li>尝试不同的提取算法</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentRemove, Clock } from '@element-plus/icons-vue'
import type { AdvancedExtractionResult } from '@/utils/advanced-watermark-extractor'

// Props
interface Props {
  result?: AdvancedExtractionResult | null
}

const props = withDefaults(defineProps<Props>(), {
  result: null
})

// 响应式数据
const showDebugInfo = ref(false)

// 计算属性
const getConfidenceColor = (confidence: number): string => {
  if (confidence > 0.7) return '#67c23a'
  if (confidence > 0.4) return '#e6a23c'
  return '#f56c6c'
}

const getConfidenceType = (confidence: number): string => {
  if (confidence > 0.7) return 'success'
  if (confidence > 0.4) return 'warning'
  return 'danger'
}

const getConfidenceDescription = (confidence: number): string => {
  if (confidence > 0.8) return '非常可信'
  if (confidence > 0.6) return '较为可信'
  if (confidence > 0.4) return '一般可信'
  if (confidence > 0.2) return '可信度较低'
  return '不可信'
}

// 格式化时间戳
const formatTimestamp = (timestamp?: number): string => {
  if (!timestamp) return '未知时间'
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取相对时间
const getTimeAgo = (timestamp?: number): string => {
  if (!timestamp) return ''
  
  const now = Date.now()
  const diff = now - timestamp
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

// 导出结果
const exportResult = () => {
  if (!props.result || !props.result.success) return
  
  const exportData = {
    extractionTime: new Date().toISOString(),
    confidence: props.result.confidence,
    extractionMethod: props.result.extractionMethod,
    watermarkData: props.result.watermarkData,
    debugInfo: props.result.debugInfo
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
    type: 'application/json' 
  })
  
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `watermark-extraction-${Date.now()}.json`
  link.click()
  
  URL.revokeObjectURL(url)
  ElMessage.success('结果已导出')
}
</script>

<style lang="scss" scoped>
.extraction-result-viewer {
  .no-result {
    text-align: center;
    padding: 40px;
    color: #909399;
    
    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  
  .success-result {
    h3 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .error-result {
    h4 {
      margin: 16px 0 8px 0;
      color: #303133;
      font-size: 14px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        line-height: 1.5;
      }
    }
  }
  
  .params-display {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .confidence-chart {
    margin-top: 12px;
    
    .confidence-bar {
      height: 20px;
      background: #f5f7fa;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
      
      .confidence-fill {
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 10px;
      }
    }
    
    .confidence-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
    }
    
    .confidence-description {
      text-align: center;
      margin-top: 12px;
    }
  }
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-progress-bar__outer) {
  background-color: #f5f7fa;
}
</style>
