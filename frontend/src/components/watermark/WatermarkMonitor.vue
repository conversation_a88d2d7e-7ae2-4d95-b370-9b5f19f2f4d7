<template>
  <div v-if="showMonitor" class="watermark-monitor">
    <el-card size="small" class="monitor-card">
      <template #header>
        <div class="monitor-header">
          <span>🔒 水印监控</span>
          <el-button type="text" size="small" @click="toggleExpanded">
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>
      
      <div class="monitor-content">
        <!-- 基本状态 -->
        <div class="status-row">
          <el-tag :type="getStatusType(status)" size="small">
            {{ getStatusText(status) }}
          </el-tag>
          <span class="status-time">{{ formatTime(lastUpdate) }}</span>
        </div>
        
        <!-- 详细信息 -->
        <div v-if="expanded" class="details">
          <el-descriptions :column="1" size="small" border>
            <el-descriptions-item label="环境">
              {{ environment }}
            </el-descriptions-item>
            <el-descriptions-item label="强度">
              {{ (strength * 100).toFixed(3) }}%
            </el-descriptions-item>
            <el-descriptions-item label="技术">
              {{ techniques.join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="刷新间隔">
              {{ refreshInterval / 1000 }}秒
            </el-descriptions-item>
            <el-descriptions-item label="会话ID">
              <code>{{ sessionId || '未生成' }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="当前用户">
              {{ getCurrentUser() }}
            </el-descriptions-item>
            <el-descriptions-item label="当前路径">
              {{ getCurrentPath() }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 操作按钮 -->
          <div class="actions" style="margin-top: 12px;">
            <el-space size="small">
              <el-button type="primary" size="small" @click="restart">
                重启
              </el-button>
              <el-button type="danger" size="small" @click="destroy">
                停止
              </el-button>
              <el-button type="info" size="small" @click="exportLogs">
                导出日志
              </el-button>
            </el-space>
          </div>
          
          <!-- 日志 -->
          <div class="logs" style="margin-top: 12px;">
            <h4>最近日志</h4>
            <div class="log-container">
              <div 
                v-for="(log, index) in recentLogs" 
                :key="index"
                class="log-item"
                :class="log.level"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { watermarkService, type WatermarkStatus } from '@/services/watermark-service'
import { getCurrentWatermarkConfig, getEnvironment } from '@/config/watermark.config'

// 响应式数据
const status = ref<WatermarkStatus>('disabled')
const lastUpdate = ref<number>(Date.now())
const expanded = ref(false)
const sessionId = ref<string>('')
const currentUser = ref<string>('')
const currentPath = ref<string>('')

// 日志系统
interface LogEntry {
  timestamp: number
  level: 'info' | 'warn' | 'error'
  message: string
}

const recentLogs = ref<LogEntry[]>([])
const maxLogs = 20

// 配置信息
const config = getCurrentWatermarkConfig()
const environment = getEnvironment()
const strength = config.strength
const techniques = config.techniques
const refreshInterval = config.refreshInterval

// 显示条件
const showMonitor = computed(() => {
  return import.meta.env.MODE === 'development' && config.debugMode
})

// 状态相关
const getStatusType = (status: WatermarkStatus): string => {
  switch (status) {
    case 'active': return 'success'
    case 'error': return 'danger'
    case 'initializing': return 'warning'
    case 'paused': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status: WatermarkStatus): string => {
  switch (status) {
    case 'active': return '运行中'
    case 'error': return '错误'
    case 'initializing': return '初始化'
    case 'paused': return '暂停'
    case 'disabled': return '已禁用'
    default: return '未知'
  }
}

// 时间格式化
const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

// 添加日志
const addLog = (level: LogEntry['level'], message: string) => {
  recentLogs.value.unshift({
    timestamp: Date.now(),
    level,
    message
  })
  
  // 保持日志数量限制
  if (recentLogs.value.length > maxLogs) {
    recentLogs.value = recentLogs.value.slice(0, maxLogs)
  }
}

// 获取当前用户
const getCurrentUser = (): string => {
  const userStore = (window as any).__USER_STORE__
  return userStore?.name || currentUser.value || '未知用户'
}

// 获取当前路径
const getCurrentPath = (): string => {
  return window.location.pathname || currentPath.value || '/'
}

// 更新服务信息
const updateServiceInfo = () => {
  const info = watermarkService.getServiceInfo()
  status.value = info.status
  sessionId.value = info.sessionId
  currentUser.value = getCurrentUser()
  currentPath.value = getCurrentPath()
  lastUpdate.value = Date.now()
}

// 操作方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const restart = async () => {
  addLog('info', '用户手动重启水印服务')

  try {
    const user = getCurrentUser()
    const path = getCurrentPath()

    addLog('info', `重启参数: 用户=${user}, 路径=${path}`)

    const success = await watermarkService.restart({
      user: user,
      path: path,
      params: {},
      timestamp: Date.now()
    })

    if (success) {
      addLog('info', '水印服务重启成功')
      // 延迟更新服务信息，确保新的会话ID已生成
      setTimeout(() => {
        updateServiceInfo()
        addLog('info', `新会话ID: ${watermarkService.getSessionId()}`)
      }, 300)
    } else {
      addLog('error', '水印服务重启失败')
    }
  } catch (error) {
    addLog('error', `重启失败: ${error}`)
    console.error('水印重启错误:', error)
  }
}

const destroy = async () => {
  addLog('warn', '用户手动停止水印服务')
  await watermarkService.destroy()
  addLog('info', '水印服务已停止')
}

const exportLogs = () => {
  const logData = {
    timestamp: new Date().toISOString(),
    environment,
    config,
    status: status.value,
    logs: recentLogs.value
  }
  
  const blob = new Blob([JSON.stringify(logData, null, 2)], { 
    type: 'application/json' 
  })
  
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `watermark-logs-${Date.now()}.json`
  link.click()
  
  URL.revokeObjectURL(url)
  addLog('info', '日志已导出')
}

// 生命周期
onMounted(() => {
  // 设置水印服务监听
  watermarkService.setEventListeners({
    onStatusChange: (newStatus) => {
      status.value = newStatus
      lastUpdate.value = Date.now()
      addLog('info', `状态变更: ${getStatusText(newStatus)}`)

      // 状态变化时更新服务信息
      updateServiceInfo()
    },
    onError: (error) => {
      addLog('error', `错误: ${error.message}`)
    }
  })

  // 初始化状态和服务信息
  updateServiceInfo()
  addLog('info', '水印监控器已启动')

  // 定期更新服务信息
  const updateInterval = setInterval(() => {
    updateServiceInfo()
  }, 2000) // 每2秒更新一次

  onUnmounted(() => {
    clearInterval(updateInterval)
  })
})
</script>

<style lang="scss" scoped>
.watermark-monitor {
  position: fixed;
  top: 150px;
  right: 10px;
  z-index: 9998;
  width: 320px;
  max-height: 80vh;
  overflow-y: auto;
  
  .monitor-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e4e7ed;
    
    :deep(.el-card__header) {
      padding: 8px 12px;
      background: #f5f7fa;
    }
    
    :deep(.el-card__body) {
      padding: 12px;
    }
  }
  
  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
  }
  
  .monitor-content {
    .status-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .status-time {
        font-size: 11px;
        color: #909399;
      }
    }
    
    .details {
      margin-top: 12px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: #303133;
      }
      
      code {
        font-size: 10px;
        background: #f5f7fa;
        padding: 2px 4px;
        border-radius: 2px;
      }
    }
    
    .log-container {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 8px;
      background: #fafafa;
      
      .log-item {
        display: flex;
        gap: 8px;
        margin-bottom: 4px;
        font-size: 11px;
        line-height: 1.4;
        
        .log-time {
          color: #909399;
          white-space: nowrap;
        }
        
        .log-message {
          flex: 1;
        }
        
        &.info .log-message {
          color: #303133;
        }
        
        &.warn .log-message {
          color: #e6a23c;
        }
        
        &.error .log-message {
          color: #f56c6c;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .watermark-monitor {
    width: 280px;
    right: 5px;
  }
}
</style>
